/*!
# Wellbot Bridge CLI

Professional command-line interface for the Wellbot Bridge Service.
Provides interactive management for WhatsApp AI integration with beautiful cliclack interface.

## Usage

```bash
# Run the interactive CLI
cargo run --bin wellbot-cli --features examples

# Or build and run
cargo build --bin wellbot-cli --features examples
./target/debug/wellbot-cli
```

## Features

- 🔧 Service configuration and management
- 🔐 JID authorization management
- 🚀 Service control (start/stop/restart)
- 📊 Real-time monitoring and statistics
- 🎯 Interactive demonstrations
- 🧪 Connection testing
- 💾 Persistent configuration storage
*/

use std::process;

use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use wellbot_bridge::cli::WellbotCli;

#[tokio::main]
async fn main() {
    // Initialize logging
    if let Err(e) = init_logging().await {
        eprintln!("Failed to initialize logging: {}", e);
        process::exit(1);
    }

    // Create and run CLI
    match WellbotCli::new() {
        Ok(mut cli) => {
            if let Err(e) = cli.run().await {
                eprintln!("CLI error: {}", e);
                process::exit(1);
            }
        }
        Err(e) => {
            eprintln!("Failed to initialize CLI: {}", e);
            process::exit(1);
        }
    }
}

/// Initialize structured logging for CLI
async fn init_logging() -> Result<(), Box<dyn std::error::Error>> {
    let log_level = std::env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string());
    let log_format = std::env::var("LOG_FORMAT").unwrap_or_else(|_| "pretty".to_string());

    let env_filter = format!("wellbot_bridge={}", log_level);

    let subscriber = tracing_subscriber::registry().with(
        tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| env_filter.into()),
    );

    match log_format.as_str() {
        "json" => {
            subscriber
                .with(tracing_subscriber::fmt::layer().json().with_target(false))
                .init();
        }
        _ => {
            subscriber
                .with(tracing_subscriber::fmt::layer().with_target(false))
                .init();
        }
    }

    Ok(())
}
