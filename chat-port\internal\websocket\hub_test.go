package websocket

import (
	"context"
	"testing"
	"time"
)

func TestNewHub(t *testing.T) {
	config := &HubConfig{
		MaxClients:          100,
		CleanupInterval:     30 * time.Second,
		ClientTimeout:       5 * time.Minute,
		BroadcastBufferSize: 1000,
	}

	hub := NewHub(config)

	if hub == nil {
		t.Fatal("Expected hub to be created")
	}

	if hub.config.MaxClients != 100 {
		t.<PERSON><PERSON>("Expected MaxClients 100, got %d", hub.config.MaxClients)
	}

	if len(hub.clients) != 0 {
		t.<PERSON><PERSON><PERSON>("Expected no initial clients, got %d", len(hub.clients))
	}

	stats := hub.GetStats()
	if stats.CurrentConnections != 0 {
		t.<PERSON><PERSON>("Expected 0 current connections, got %d", stats.CurrentConnections)
	}

	if stats.StartTime.IsZero() {
		t.<PERSON>rror("Expected start time to be set")
	}
}

func TestNewHub_DefaultConfig(t *testing.T) {
	hub := NewHub(nil)

	if hub == nil {
		t.Fatal("Expected hub to be created with default config")
	}

	defaultConfig := DefaultHubConfig()
	if hub.config.MaxClients != defaultConfig.MaxClients {
		t.Errorf("Expected default MaxClients %d, got %d", defaultConfig.MaxClients, hub.config.MaxClients)
	}
}

func TestDefaultHubConfig(t *testing.T) {
	config := DefaultHubConfig()

	if config.MaxClients <= 0 {
		t.Error("Expected positive MaxClients")
	}

	if config.CleanupInterval <= 0 {
		t.Error("Expected positive CleanupInterval")
	}

	if config.ClientTimeout <= 0 {
		t.Error("Expected positive ClientTimeout")
	}

	if config.BroadcastBufferSize <= 0 {
		t.Error("Expected positive BroadcastBufferSize")
	}
}

func TestHub_RegisterClient(t *testing.T) {
	hub := NewHub(&HubConfig{
		MaxClients:          2,
		CleanupInterval:     30 * time.Second,
		ClientTimeout:       5 * time.Minute,
		BroadcastBufferSize: 10,
	})

	// Start hub in background
	go hub.Run()
	defer hub.Stop()

	// Create mock clients
	client1 := &Client{
		id:           "client1",
		send:         make(chan []byte, 10),
		ctx:          context.Background(),
		connectedAt:  time.Now(),
		lastActivity: time.Now(),
	}

	client2 := &Client{
		id:           "client2",
		send:         make(chan []byte, 10),
		ctx:          context.Background(),
		connectedAt:  time.Now(),
		lastActivity: time.Now(),
	}

	client3 := &Client{
		id:           "client3",
		send:         make(chan []byte, 10),
		ctx:          context.Background(),
		connectedAt:  time.Now(),
		lastActivity: time.Now(),
	}

	// Register first client
	err := hub.RegisterClient(client1)
	if err != nil {
		t.Errorf("Failed to register client1: %v", err)
	}

	// Wait a bit for registration to process
	time.Sleep(10 * time.Millisecond)

	if hub.GetClientCount() != 1 {
		t.Errorf("Expected 1 client, got %d", hub.GetClientCount())
	}

	// Register second client
	err = hub.RegisterClient(client2)
	if err != nil {
		t.Errorf("Failed to register client2: %v", err)
	}

	time.Sleep(10 * time.Millisecond)

	if hub.GetClientCount() != 2 {
		t.Errorf("Expected 2 clients, got %d", hub.GetClientCount())
	}

	// Try to register third client (should fail due to max limit)
	err = hub.RegisterClient(client3)
	if err != ErrMaxClients {
		t.Errorf("Expected ErrMaxClients, got %v", err)
	}
}

func TestHub_BroadcastToAll(t *testing.T) {
	hub := NewHub(&HubConfig{
		MaxClients:          10,
		CleanupInterval:     30 * time.Second,
		ClientTimeout:       5 * time.Minute,
		BroadcastBufferSize: 10,
	})

	go hub.Run()
	defer hub.Stop()

	// Create clients with subscriptions that should receive broadcast to all
	client1 := &Client{
		id:            "client1",
		send:          make(chan []byte, 10),
		ctx:           context.Background(),
		connectedAt:   time.Now(),
		lastActivity:  time.Now(),
		subscriptions: map[SubscriptionType]bool{SubscriptionAll: true},
	}

	client2 := &Client{
		id:            "client2",
		send:          make(chan []byte, 10),
		ctx:           context.Background(),
		connectedAt:   time.Now(),
		lastActivity:  time.Now(),
		subscriptions: map[SubscriptionType]bool{SubscriptionAll: true}, // Also subscribe to all
	}

	// Register clients
	hub.RegisterClient(client1)
	hub.RegisterClient(client2)
	time.Sleep(10 * time.Millisecond)

	// Broadcast message to all
	msg, _ := NewMessage(MessageTypePing, nil)
	hub.BroadcastToAll(msg)

	// Wait for broadcast to process
	time.Sleep(50 * time.Millisecond)

	// Both clients should receive the message when broadcasting to all
	select {
	case <-client1.send:
		// Expected
	case <-time.After(100 * time.Millisecond):
		t.Error("Client1 should have received the broadcast message")
	}

	select {
	case <-client2.send:
		// Expected
	case <-time.After(100 * time.Millisecond):
		t.Error("Client2 should have received the broadcast message")
	}
}

func TestHub_BroadcastToSubscription(t *testing.T) {
	hub := NewHub(&HubConfig{
		MaxClients:          10,
		CleanupInterval:     30 * time.Second,
		ClientTimeout:       5 * time.Minute,
		BroadcastBufferSize: 10,
	})

	go hub.Run()
	defer hub.Stop()

	// Create clients with different subscriptions
	client1 := &Client{
		id:            "client1",
		send:          make(chan []byte, 10),
		ctx:           context.Background(),
		connectedAt:   time.Now(),
		lastActivity:  time.Now(),
		subscriptions: map[SubscriptionType]bool{SubscriptionWhatsApp: true},
	}

	client2 := &Client{
		id:            "client2",
		send:          make(chan []byte, 10),
		ctx:           context.Background(),
		connectedAt:   time.Now(),
		lastActivity:  time.Now(),
		subscriptions: map[SubscriptionType]bool{SubscriptionMetrics: true},
	}

	// Register clients
	hub.RegisterClient(client1)
	hub.RegisterClient(client2)
	time.Sleep(10 * time.Millisecond)

	// Broadcast WhatsApp message
	msg, _ := NewIncomingWhatsAppMessage("test", "hello", "msg1")
	hub.BroadcastToSubscription(msg, SubscriptionWhatsApp)

	time.Sleep(10 * time.Millisecond)

	// Client1 should receive the message
	select {
	case <-client1.send:
		// Expected
	case <-time.After(100 * time.Millisecond):
		t.Error("Client1 should have received the WhatsApp message")
	}

	// Client2 should not receive it
	select {
	case <-client2.send:
		t.Error("Client2 should not have received the WhatsApp message")
	case <-time.After(50 * time.Millisecond):
		// Expected
	}
}

func TestHub_GetStats(t *testing.T) {
	hub := NewHub(DefaultHubConfig())

	stats := hub.GetStats()

	if stats.TotalConnections != 0 {
		t.Errorf("Expected 0 total connections, got %d", stats.TotalConnections)
	}

	if stats.CurrentConnections != 0 {
		t.Errorf("Expected 0 current connections, got %d", stats.CurrentConnections)
	}

	if stats.MessagesSent != 0 {
		t.Errorf("Expected 0 messages sent, got %d", stats.MessagesSent)
	}

	if stats.StartTime.IsZero() {
		t.Error("Expected start time to be set")
	}
}

func TestHub_GetClientByID(t *testing.T) {
	hub := NewHub(DefaultHubConfig())

	go hub.Run()
	defer hub.Stop()

	client := &Client{
		id:           "test-client",
		send:         make(chan []byte, 10),
		ctx:          context.Background(),
		connectedAt:  time.Now(),
		lastActivity: time.Now(),
	}

	// Register client
	hub.RegisterClient(client)
	time.Sleep(10 * time.Millisecond)

	// Find client by ID
	found := hub.GetClientByID("test-client")
	if found == nil {
		t.Error("Expected to find client by ID")
	}

	if found.ID() != "test-client" {
		t.Errorf("Expected client ID 'test-client', got %s", found.ID())
	}

	// Try to find non-existent client
	notFound := hub.GetClientByID("non-existent")
	if notFound != nil {
		t.Error("Expected not to find non-existent client")
	}
}

func TestHub_Stop(t *testing.T) {
	hub := NewHub(DefaultHubConfig())

	// Start hub
	go hub.Run()

	// Stop hub
	hub.Stop()

	// Context should be cancelled
	select {
	case <-hub.ctx.Done():
		// Expected
	case <-time.After(100 * time.Millisecond):
		t.Error("Expected hub context to be cancelled")
	}
}

func TestHub_CleanupInactiveClients(t *testing.T) {
	hub := NewHub(&HubConfig{
		MaxClients:          10,
		CleanupInterval:     50 * time.Millisecond, // Short for testing
		ClientTimeout:       30 * time.Millisecond, // Very short for testing
		BroadcastBufferSize: 10,
	})

	go hub.Run()
	defer hub.Stop()

	// Create client with old activity
	client := &Client{
		id:           "inactive-client",
		send:         make(chan []byte, 10),
		ctx:          context.Background(),
		connectedAt:  time.Now(),
		lastActivity: time.Now().Add(-time.Minute), // Very old activity
	}

	// Register client
	hub.RegisterClient(client)
	time.Sleep(20 * time.Millisecond)

	if hub.GetClientCount() != 1 {
		t.Errorf("Expected 1 client initially, got %d", hub.GetClientCount())
	}

	// Wait for cleanup to run (multiple cycles to ensure cleanup happens)
	for i := 0; i < 10; i++ {
		time.Sleep(60 * time.Millisecond)
		if hub.GetClientCount() == 0 {
			break
		}
	}

	// Client should be removed due to inactivity
	if hub.GetClientCount() != 0 {
		t.Errorf("Expected 0 clients after cleanup, got %d", hub.GetClientCount())
	}

	stats := hub.GetStats()
	if stats.ClientsTimedOut == 0 {
		t.Error("Expected at least one client to be timed out")
	}
}
