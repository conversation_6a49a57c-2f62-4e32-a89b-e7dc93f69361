use chrono::{DateTime, NaiveDateTime, Utc};
use serde::{Deserialize, Deserializer, Serialize};
use uuid::Uuid;

/// WebSocket message types from chat-port service
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WebSocketMessage {
    #[serde(rename = "welcome")]
    Welcome {
        timestamp: DateTime<Utc>,
        data: WelcomeData,
    },
    #[serde(rename = "incoming_whatsapp")]
    IncomingWhatsApp {
        timestamp: DateTime<Utc>,
        data: IncomingWhatsAppData,
    },
    #[serde(rename = "status_update")]
    StatusUpdate {
        timestamp: DateTime<Utc>,
        data: StatusUpdateData,
    },
    #[serde(rename = "error")]
    Error {
        timestamp: DateTime<Utc>,
        error: String,
        data: Option<ErrorData>,
    },
    #[serde(rename = "ping")]
    Ping { timestamp: DateTime<Utc> },
    #[serde(rename = "pong")]
    Pong { timestamp: DateTime<Utc> },
}

/// Welcome message data from chat-port
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WelcomeData {
    pub client_id: String,
    pub server_time: DateTime<Utc>,
    pub version: String,
    pub capabilities: Vec<String>,
}

/// Incoming WhatsApp message data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct IncomingWhatsAppData {
    pub from: String,
    pub message: String,
    pub timestamp: DateTime<Utc>,
    pub message_id: String,
}

/// Status update data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StatusUpdateData {
    pub service: String,
    pub status: String,
    pub details: Option<String>,
    pub timestamp: DateTime<Utc>,
}

/// Error data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorData {
    pub code: String,
    pub message: String,
    pub details: Option<String>,
}

/// Subscription message to send to chat-port WebSocket
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriptionMessage {
    #[serde(rename = "type")]
    pub message_type: String,
    pub timestamp: DateTime<Utc>,
    pub data: SubscriptionData,
}

/// Subscription data
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SubscriptionData {
    pub action: String,
    pub subscription: String,
}

/// Custom deserializer for timestamps that may not have timezone info
fn deserialize_timestamp<'de, D>(deserializer: D) -> Result<DateTime<Utc>, D::Error>
where
    D: Deserializer<'de>,
{
    let s = String::deserialize(deserializer)?;

    // Try parsing as UTC timestamp first
    if let Ok(dt) = DateTime::parse_from_rfc3339(&s) {
        return Ok(dt.with_timezone(&Utc));
    }

    // Try parsing as naive datetime and assume UTC
    if let Ok(naive_dt) = NaiveDateTime::parse_from_str(&s, "%Y-%m-%dT%H:%M:%S%.f") {
        return Ok(DateTime::from_naive_utc_and_offset(naive_dt, Utc));
    }

    // Fallback: try without microseconds
    if let Ok(naive_dt) = NaiveDateTime::parse_from_str(&s, "%Y-%m-%dT%H:%M:%S") {
        return Ok(DateTime::from_naive_utc_and_offset(naive_dt, Utc));
    }

    Err(serde::de::Error::custom(format!(
        "Unable to parse timestamp: {}",
        s
    )))
}

/// AI service request (genuis API)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExplanationRequest {
    pub prompt: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub temperature: Option<f32>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_tokens: Option<i32>,
}

/// AI service response (genuis API)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ExplanationResponse {
    pub content: String,
    pub model: String,
    #[serde(deserialize_with = "deserialize_timestamp")]
    pub timestamp: DateTime<Utc>,
    pub tokens_used: i32,
    pub processing_time_ms: f64,
}

/// WhatsApp send message request (chat-port API)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SendMessageRequest {
    pub number: String,
    pub message: String,
}

/// WhatsApp send message response (chat-port API)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SendMessageResponse {
    pub success: bool,
    pub message_id: Option<String>,
    pub error: Option<String>,
}

/// Internal message processing context
#[derive(Debug, Clone)]
pub struct MessageContext {
    pub id: Uuid,
    pub whatsapp_from: String,
    pub whatsapp_message_id: String,
    pub original_message: String,
    pub received_at: DateTime<Utc>,
    pub processing_started_at: Option<DateTime<Utc>>,
    pub ai_response_received_at: Option<DateTime<Utc>>,
    pub sent_at: Option<DateTime<Utc>>,
}

impl MessageContext {
    pub fn new(whatsapp_data: &IncomingWhatsAppData) -> Self {
        Self {
            id: Uuid::now_v7(),
            whatsapp_from: whatsapp_data.from.clone(),
            whatsapp_message_id: whatsapp_data.message_id.clone(),
            original_message: whatsapp_data.message.clone(),
            received_at: whatsapp_data.timestamp,
            processing_started_at: None,
            ai_response_received_at: None,
            sent_at: None,
        }
    }

    pub fn start_processing(&mut self) {
        self.processing_started_at = Some(Utc::now());
    }

    pub fn ai_response_received(&mut self) {
        self.ai_response_received_at = Some(Utc::now());
    }

    pub fn message_sent(&mut self) {
        self.sent_at = Some(Utc::now());
    }

    pub fn total_processing_time_ms(&self) -> Option<i64> {
        if let (Some(start), Some(end)) = (self.processing_started_at, self.sent_at) {
            Some((end - start).num_milliseconds())
        } else {
            None
        }
    }
}
