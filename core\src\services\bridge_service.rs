/*!
# Bridge Service

Comprehensive bridge service that orchestrates WhatsApp messaging with AI processing.
Handles WebSocket communication, JID authorization, message routing, and AI integration.
*/

use std::{sync::Arc, time::Duration};

use tokio::{
    sync::{Mutex, RwLock, broadcast},
    task::<PERSON><PERSON><PERSON><PERSON><PERSON>,
    time::timeout,
};
use tracing::{debug, error, info, instrument, warn};

use crate::{
    config::Config,
    error::{BridgeError, BridgeResult},
    services::{
        ai_client::AiClient, chat_port_client::ChatPortClient, message_processor::MessageProcessor,
    },
    types::{ExplanationRequest, IncomingWhatsAppData, SendMessageRequest, WebSocketMessage},
};

/// Bridge service statistics
#[derive(Debug, <PERSON><PERSON>, <PERSON><PERSON>ult)]
pub struct BridgeStats {
    pub messages_processed: u64,
    pub messages_authorized: u64,
    pub messages_unauthorized: u64,
    pub ai_requests_sent: u64,
    pub ai_responses_received: u64,
    pub websocket_reconnections: u64,
    pub errors_encountered: u64,
}

/// Bridge service configuration for runtime behavior
#[derive(Debug, <PERSON><PERSON>)]
pub struct BridgeServiceConfig {
    pub allowed_jids: Vec<String>,
    pub enable_background_processing: bool,
    pub websocket_auto_reconnect: bool,
    pub ai_processing_enabled: bool,
    pub max_concurrent_ai_requests: usize,
}

impl Default for BridgeServiceConfig {
    fn default() -> Self {
        Self {
            allowed_jids: vec![],
            enable_background_processing: true,
            websocket_auto_reconnect: true,
            ai_processing_enabled: true,
            max_concurrent_ai_requests: 5,
        }
    }
}

/// Main bridge service that orchestrates all components
#[derive(Debug)]
pub struct BridgeService {
    config: Config,
    runtime_config: Arc<RwLock<BridgeServiceConfig>>,
    ai_client: Arc<AiClient>,
    chat_port_client: Arc<ChatPortClient>,
    message_processor: Arc<MessageProcessor>,
    stats: Arc<Mutex<BridgeStats>>,

    // Communication channels
    message_tx: broadcast::Sender<IncomingWhatsAppData>,
    shutdown_tx: broadcast::Sender<()>,

    // Background task handles
    websocket_handle: Arc<Mutex<Option<JoinHandle<()>>>>,
    processing_handle: Arc<Mutex<Option<JoinHandle<()>>>>,
}

impl BridgeService {
    /// Create a new bridge service with all components
    #[instrument(skip(config))]
    pub async fn new(config: Config) -> BridgeResult<Self> {
        Self::new_with_runtime_config(config, BridgeServiceConfig::default()).await
    }

    /// Create a new bridge service with custom runtime configuration
    #[instrument(skip(config, runtime_config))]
    pub async fn new_with_runtime_config(
        config: Config,
        runtime_config: BridgeServiceConfig,
    ) -> BridgeResult<Self> {
        info!("🚀 Initializing Wellbot Bridge Service components...");

        // Initialize AI client
        let ai_client = Arc::new(AiClient::new(config.ai_service.clone())?);
        info!("✅ AI client initialized");

        // Initialize chat-port client
        let chat_port_client = Arc::new(ChatPortClient::new(config.chat_port.clone())?);
        info!("✅ Chat-port client initialized");

        // Initialize message processor with AI client integration
        let message_processor = Arc::new(MessageProcessor::new_with_ai_client(
            config.bridge.clone(),
            chat_port_client.clone(),
            Some(ai_client.clone()),
            runtime_config.allowed_jids.clone(),
        ));
        info!(
            "✅ Message processor initialized with AI integration and {} allowed JIDs",
            runtime_config.allowed_jids.len()
        );

        // Create communication channels
        let (message_tx, _) = broadcast::channel(1000);
        let (shutdown_tx, _) = broadcast::channel(10);

        let service = Self {
            config,
            runtime_config: Arc::new(RwLock::new(runtime_config)),
            ai_client,
            chat_port_client,
            message_processor,
            stats: Arc::new(Mutex::new(BridgeStats::default())),
            message_tx,
            shutdown_tx,
            websocket_handle: Arc::new(Mutex::new(None)),
            processing_handle: Arc::new(Mutex::new(None)),
        };

        info!("🎉 Bridge service initialized successfully");
        Ok(service)
    }

    /// Start the bridge service with background processing
    #[instrument(skip(self))]
    pub async fn start(&self) -> BridgeResult<()> {
        info!("🚀 Starting Wellbot Bridge Service...");

        let runtime_config = self.runtime_config.read().await;

        if runtime_config.enable_background_processing {
            self.start_background_tasks().await?;
            info!("✅ Background processing enabled");
        }

        if runtime_config.websocket_auto_reconnect {
            self.start_websocket_listener().await?;
            info!("✅ WebSocket auto-reconnect enabled");
        }

        info!("🎉 Bridge service started successfully");
        Ok(())
    }

    /// Stop the bridge service gracefully
    #[instrument(skip(self))]
    pub async fn stop(&self) -> BridgeResult<()> {
        info!("🛑 Stopping Wellbot Bridge Service...");

        // Send shutdown signal
        if let Err(e) = self.shutdown_tx.send(()) {
            warn!("Failed to send shutdown signal: {}", e);
        }

        // Stop background tasks
        self.stop_background_tasks().await;

        info!("✅ Bridge service stopped gracefully");
        Ok(())
    }

    /// Get current service statistics
    pub async fn get_stats(&self) -> BridgeStats {
        self.stats.lock().await.clone()
    }

    /// Update allowed JIDs list
    #[instrument(skip(self, jids))]
    pub async fn update_allowed_jids(&self, jids: Vec<String>) -> BridgeResult<()> {
        info!("📝 Updating allowed JIDs list with {} entries", jids.len());

        // Update runtime config
        {
            let mut config = self.runtime_config.write().await;
            config.allowed_jids = jids.clone();
        }

        // Update message processor
        for jid in &jids {
            self.message_processor.add_allowed_jid(jid.clone()).await?;
        }

        info!("✅ Allowed JIDs updated successfully");
        Ok(())
    }

    /// Add a single JID to the allowed list
    #[instrument(skip(self))]
    pub async fn add_allowed_jid(&self, jid: String) -> BridgeResult<()> {
        info!("➕ Adding JID to allowed list: {}", jid);

        // Update runtime config
        {
            let mut config = self.runtime_config.write().await;
            if !config.allowed_jids.contains(&jid) {
                config.allowed_jids.push(jid.clone());
            }
        }

        // Update message processor
        self.message_processor.add_allowed_jid(jid.clone()).await?;

        info!("✅ JID added successfully: {}", jid);
        Ok(())
    }

    /// Remove a JID from the allowed list
    #[instrument(skip(self))]
    pub async fn remove_allowed_jid(&self, jid: &str) -> BridgeResult<()> {
        info!("➖ Removing JID from allowed list: {}", jid);

        // Update runtime config
        {
            let mut config = self.runtime_config.write().await;
            config.allowed_jids.retain(|j| j != jid);
        }

        // Update message processor
        self.message_processor.remove_allowed_jid(jid).await?;

        info!("✅ JID removed successfully: {}", jid);
        Ok(())
    }

    /// Get current allowed JIDs
    pub async fn get_allowed_jids(&self) -> Vec<String> {
        self.runtime_config.read().await.allowed_jids.clone()
    }

    /// Process a single WhatsApp message with AI integration
    #[instrument(skip(self, message))]
    pub async fn process_whatsapp_message(
        &self,
        message: IncomingWhatsAppData,
    ) -> BridgeResult<()> {
        debug!("📨 Processing WhatsApp message from: {}", message.from);

        // Update stats
        {
            let mut stats = self.stats.lock().await;
            stats.messages_processed += 1;
        }

        // Check if JID is authorized
        let runtime_config = self.runtime_config.read().await;
        if !runtime_config.allowed_jids.contains(&message.from) {
            warn!("🚫 Unauthorized message from JID: {}", message.from);

            let mut stats = self.stats.lock().await;
            stats.messages_unauthorized += 1;

            return Err(Box::new(BridgeError::Authentication(format!(
                "JID not authorized: {}",
                message.from
            ))));
        }

        // Update authorized stats
        {
            let mut stats = self.stats.lock().await;
            stats.messages_authorized += 1;
        }

        // Process with AI if enabled
        if runtime_config.ai_processing_enabled {
            self.process_with_ai(message).await?;
        } else {
            // Use message processor for basic processing
            self.message_processor.process_message(message).await?;
        }

        Ok(())
    }

    /// Process message with AI integration
    #[instrument(skip(self, message))]
    async fn process_with_ai(&self, message: IncomingWhatsAppData) -> BridgeResult<()> {
        debug!("🤖 Processing message with AI: {}", message.message_id);

        // Update AI request stats
        {
            let mut stats = self.stats.lock().await;
            stats.ai_requests_sent += 1;
        }

        // Create AI request
        let ai_request = ExplanationRequest {
            prompt: format!(
                "WhatsApp message from {}: {}",
                message.from, message.message
            ),
            temperature: None,
            max_tokens: None,
        };

        // Send to AI service with timeout
        let ai_timeout = Duration::from_secs(self.config.ai_service.timeout_secs);
        let ai_response =
            timeout(ai_timeout, self.ai_client.generate_explanation(ai_request)).await;

        match ai_response {
            Ok(Ok(response)) => {
                debug!(
                    "✅ AI response received for message: {}",
                    message.message_id
                );

                // Update AI response stats
                {
                    let mut stats = self.stats.lock().await;
                    stats.ai_responses_received += 1;
                }

                // Send response back via WhatsApp
                let send_request = SendMessageRequest {
                    number: message.from.clone(),
                    message: response.content,
                };

                if let Err(e) = self.chat_port_client.send_message(send_request).await {
                    error!("Failed to send AI response via WhatsApp: {}", e);

                    let mut stats = self.stats.lock().await;
                    stats.errors_encountered += 1;

                    return Err(e);
                }

                info!("📤 AI response sent successfully to: {}", message.from);
            }
            Ok(Err(e)) => {
                error!("AI service error for message {}: {}", message.message_id, e);

                let mut stats = self.stats.lock().await;
                stats.errors_encountered += 1;

                // Send error response to user
                self.send_error_response(
                    &message,
                    "I'm having trouble processing your request right now. Please try again later.",
                )
                .await?;

                return Err(e);
            }
            Err(_) => {
                error!("AI request timeout for message: {}", message.message_id);

                let mut stats = self.stats.lock().await;
                stats.errors_encountered += 1;

                // Send timeout response to user
                self.send_error_response(
                    &message,
                    "Your request is taking longer than expected. Please try again.",
                )
                .await?;

                return Err(Box::new(BridgeError::Timeout(
                    "AI request timeout".to_string(),
                )));
            }
        }

        Ok(())
    }

    /// Send error response to WhatsApp user
    #[instrument(skip(self, message, error_text))]
    async fn send_error_response(
        &self,
        message: &IncomingWhatsAppData,
        error_text: &str,
    ) -> BridgeResult<()> {
        let send_request = SendMessageRequest {
            number: message.from.clone(),
            message: error_text.to_string(),
        };

        self.chat_port_client.send_message(send_request).await?;
        debug!("📤 Error response sent to: {}", message.from);
        Ok(())
    }

    /// Start background processing tasks
    #[instrument(skip(self))]
    async fn start_background_tasks(&self) -> BridgeResult<()> {
        info!("🔄 Starting background processing tasks...");

        // Start message processing task
        let processing_handle = self.spawn_message_processing_task().await;
        *self.processing_handle.lock().await = Some(processing_handle);

        info!("✅ Background tasks started");
        Ok(())
    }

    /// Start WebSocket listener
    #[instrument(skip(self))]
    async fn start_websocket_listener(&self) -> BridgeResult<()> {
        info!("🔌 Starting WebSocket listener...");

        let websocket_handle = self.spawn_websocket_task().await;
        *self.websocket_handle.lock().await = Some(websocket_handle);

        info!("✅ WebSocket listener started");
        Ok(())
    }

    /// Stop background tasks
    async fn stop_background_tasks(&self) {
        info!("🛑 Stopping background tasks...");

        // Stop processing task
        if let Some(handle) = self.processing_handle.lock().await.take() {
            handle.abort();
            debug!("✅ Processing task stopped");
        }

        // Stop WebSocket task
        if let Some(handle) = self.websocket_handle.lock().await.take() {
            handle.abort();
            debug!("✅ WebSocket task stopped");
        }

        info!("✅ All background tasks stopped");
    }

    /// Spawn message processing background task
    async fn spawn_message_processing_task(&self) -> JoinHandle<()> {
        let mut message_rx = self.message_tx.subscribe();
        let mut shutdown_rx = self.shutdown_tx.subscribe();
        let service = Arc::new(self.clone());

        tokio::spawn(async move {
            info!("🔄 Message processing task started");

            loop {
                tokio::select! {
                    message_result = message_rx.recv() => {
                        match message_result {
                            Ok(message) => {
                                debug!("📨 Background processing message: {}", message.message_id);

                                if let Err(e) = service.process_whatsapp_message(message).await {
                                    error!("Background message processing error: {}", e);
                                }
                            }
                            Err(e) => {
                                error!("Message channel error: {}", e);
                                break;
                            }
                        }
                    }
                    _ = shutdown_rx.recv() => {
                        info!("🛑 Message processing task received shutdown signal");
                        break;
                    }
                }
            }

            info!("✅ Message processing task stopped");
        })
    }

    /// Spawn WebSocket listener background task
    async fn spawn_websocket_task(&self) -> JoinHandle<()> {
        let chat_port_client = self.chat_port_client.clone();
        let message_tx = self.message_tx.clone();
        let mut shutdown_rx = self.shutdown_tx.subscribe();
        let stats = self.stats.clone();

        tokio::spawn(async move {
            info!("🔌 WebSocket listener task started");

            let mut reconnect_attempts = 0;
            let max_reconnect_attempts = 10;
            let reconnect_delay = Duration::from_secs(5);

            loop {
                tokio::select! {
                    _ = shutdown_rx.recv() => {
                        info!("🛑 WebSocket task received shutdown signal");
                        break;
                    }
                    websocket_result = Self::run_websocket_listener(
                        chat_port_client.clone(),
                        message_tx.clone(),
                        stats.clone()
                    ) => {
                        match websocket_result {
                            Ok(_) => {
                                info!("WebSocket connection closed normally");
                                break;
                            }
                            Err(e) => {
                                error!("WebSocket connection error: {}", e);

                                // Update reconnection stats
                                {
                                    let mut s = stats.lock().await;
                                    s.websocket_reconnections += 1;
                                }

                                if reconnect_attempts >= max_reconnect_attempts {
                                    error!("Max WebSocket reconnection attempts reached, giving up");
                                    break;
                                }

                                reconnect_attempts += 1;
                                warn!(
                                    "Reconnecting WebSocket in {}s (attempt {}/{})",
                                    reconnect_delay.as_secs(),
                                    reconnect_attempts,
                                    max_reconnect_attempts
                                );

                                tokio::time::sleep(reconnect_delay).await;
                            }
                        }
                    }
                }
            }

            info!("✅ WebSocket listener task stopped");
        })
    }

    /// Run WebSocket listener with message forwarding
    async fn run_websocket_listener(
        chat_port_client: Arc<ChatPortClient>,
        message_tx: broadcast::Sender<IncomingWhatsAppData>,
        stats: Arc<Mutex<BridgeStats>>,
    ) -> BridgeResult<()> {
        let mut message_handler = |message: WebSocketMessage| {
            let tx = message_tx.clone();
            let stats = stats.clone();

            Box::pin(async move {
                match message {
                    WebSocketMessage::IncomingWhatsApp { data, .. } => {
                        debug!("📨 Received WhatsApp message: {}", data.message_id);

                        if let Err(e) = tx.send(data) {
                            error!("Failed to forward message to processing queue: {}", e);
                        }
                    }
                    WebSocketMessage::StatusUpdate { data, .. } => {
                        debug!("📊 Status update: {} - {}", data.service, data.status);
                    }
                    WebSocketMessage::Error { error, .. } => {
                        error!("❌ WebSocket error: {}", error);

                        let mut s = stats.lock().await;
                        s.errors_encountered += 1;
                    }
                    _ => {
                        debug!("📡 Received other WebSocket message type");
                    }
                }
            })
        };

        chat_port_client
            .listen_for_messages(&mut message_handler)
            .await
    }

    /// Health check for the bridge service
    #[instrument(skip(self))]
    pub async fn health_check(&self) -> BridgeResult<bool> {
        debug!("🏥 Performing bridge service health check");

        // Check AI service health
        let ai_healthy = self.ai_client.health_check().await.unwrap_or(false);
        if !ai_healthy {
            warn!("AI service health check failed");
            return Ok(false);
        }

        // Check if we have allowed JIDs configured
        let runtime_config = self.runtime_config.read().await;
        if runtime_config.allowed_jids.is_empty() {
            warn!("No allowed JIDs configured");
            return Ok(false);
        }

        info!("✅ Bridge service health check passed");
        Ok(true)
    }
}

// Implement Clone for BridgeService to enable Arc sharing
impl Clone for BridgeService {
    fn clone(&self) -> Self {
        Self {
            config: self.config.clone(),
            runtime_config: self.runtime_config.clone(),
            ai_client: self.ai_client.clone(),
            chat_port_client: self.chat_port_client.clone(),
            message_processor: self.message_processor.clone(),
            stats: self.stats.clone(),
            message_tx: self.message_tx.clone(),
            shutdown_tx: self.shutdown_tx.clone(),
            websocket_handle: self.websocket_handle.clone(),
            processing_handle: self.processing_handle.clone(),
        }
    }
}
