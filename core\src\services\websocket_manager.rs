/*!
# WebSocket Manager

Enhanced WebSocket communication layer for real-time bidirectional communication
with the Go chat-port service. Provides connection pooling, heartbeat mechanisms,
and robust error handling with comprehensive metrics.
*/

use std::{
    sync::Arc,
    time::{Duration, Instant},
};

use chrono::Utc;
use futures_util::{SinkExt, StreamExt};
use serde_json::json;
use tokio::{
    sync::{Mutex, RwLock, broadcast},
    time::{interval, timeout},
};
use tokio_tungstenite::{connect_async, tungstenite::Message};
use tracing::{debug, error, info, instrument, warn};

use crate::{
    config::ChatPortConfig,
    error::{BridgeError, BridgeResult},
    types::{SubscriptionData, SubscriptionMessage, WebSocketMessage},
};

/// WebSocket connection statistics
#[derive(Debug, Clone, Default)]
pub struct WebSocketStats {
    pub connections_established: u64,
    pub connections_failed: u64,
    pub reconnections: u64,
    pub messages_sent: u64,
    pub messages_received: u64,
    pub heartbeats_sent: u64,
    pub heartbeats_received: u64,
    pub errors_encountered: u64,
    pub last_connection_time: Option<Instant>,
    pub last_message_time: Option<Instant>,
    pub connection_uptime_secs: u64,
}

/// WebSocket connection state
#[derive(Debug, Clone, PartialEq)]
pub enum ConnectionState {
    Disconnected,
    Connecting,
    Connected,
    Reconnecting,
    Failed,
}

/// Enhanced WebSocket manager with connection pooling and robust error handling
#[derive(Debug)]
pub struct WebSocketManager {
    config: ChatPortConfig,
    stats: Arc<Mutex<WebSocketStats>>,
    connection_state: Arc<RwLock<ConnectionState>>,
    message_tx: broadcast::Sender<WebSocketMessage>,
    shutdown_tx: broadcast::Sender<()>,
}

impl WebSocketManager {
    /// Create a new WebSocket manager
    pub fn new(config: ChatPortConfig) -> Self {
        let (message_tx, _) = broadcast::channel(1000);
        let (shutdown_tx, _) = broadcast::channel(10);

        Self {
            config,
            stats: Arc::new(Mutex::new(WebSocketStats::default())),
            connection_state: Arc::new(RwLock::new(ConnectionState::Disconnected)),
            message_tx,
            shutdown_tx,
        }
    }

    /// Get current connection state
    pub async fn connection_state(&self) -> ConnectionState {
        *self.connection_state.read().await
    }

    /// Get WebSocket statistics
    pub async fn get_stats(&self) -> WebSocketStats {
        self.stats.lock().await.clone()
    }

    /// Subscribe to WebSocket messages
    pub fn subscribe_messages(&self) -> broadcast::Receiver<WebSocketMessage> {
        self.message_tx.subscribe()
    }

    /// Start the WebSocket connection with automatic reconnection
    #[instrument(skip(self))]
    pub async fn start(&self) -> BridgeResult<()> {
        info!("🔌 Starting WebSocket manager...");

        let mut reconnect_attempts = 0;
        let max_attempts = self.config.max_reconnect_attempts;
        let reconnect_delay = Duration::from_secs(self.config.reconnect_delay_secs);

        loop {
            // Update connection state
            {
                let mut state = self.connection_state.write().await;
                *state = if reconnect_attempts == 0 {
                    ConnectionState::Connecting
                } else {
                    ConnectionState::Reconnecting
                };
            }

            match self.connect_and_run().await {
                Ok(_) => {
                    info!("WebSocket connection closed normally");
                    break;
                }
                Err(e) => {
                    error!("WebSocket connection error: {}", e);

                    // Update stats
                    {
                        let mut stats = self.stats.lock().await;
                        stats.connections_failed += 1;
                        stats.errors_encountered += 1;
                    }

                    // Update state
                    {
                        let mut state = self.connection_state.write().await;
                        *state = ConnectionState::Failed;
                    }

                    if reconnect_attempts >= max_attempts {
                        error!(
                            "Max reconnection attempts ({}) reached, giving up",
                            max_attempts
                        );
                        return Err(e);
                    }

                    reconnect_attempts += 1;
                    {
                        let mut stats = self.stats.lock().await;
                        stats.reconnections += 1;
                    }

                    warn!(
                        "Reconnecting in {}s (attempt {}/{})",
                        reconnect_delay.as_secs(),
                        reconnect_attempts,
                        max_attempts
                    );

                    tokio::time::sleep(reconnect_delay).await;
                }
            }
        }

        // Update final state
        {
            let mut state = self.connection_state.write().await;
            *state = ConnectionState::Disconnected;
        }

        Ok(())
    }

    /// Stop the WebSocket manager
    pub async fn stop(&self) -> BridgeResult<()> {
        info!("🛑 Stopping WebSocket manager...");

        if let Err(e) = self.shutdown_tx.send(()) {
            warn!("Failed to send shutdown signal: {}", e);
        }

        // Update state
        {
            let mut state = self.connection_state.write().await;
            *state = ConnectionState::Disconnected;
        }

        info!("✅ WebSocket manager stopped");
        Ok(())
    }

    /// Internal method to establish connection and run message loop
    async fn connect_and_run(&self) -> BridgeResult<()> {
        info!(
            "🔌 Connecting to chat-port WebSocket: {}",
            self.config.websocket_url
        );

        let connection_start = Instant::now();

        // Connect with timeout
        let (ws_stream, _) = timeout(
            Duration::from_secs(self.config.connection_timeout_secs),
            connect_async(&self.config.websocket_url),
        )
        .await
        .map_err(|_| BridgeError::Timeout("WebSocket connection timeout".to_string()))?
        .map_err(BridgeError::WebSocket)?;

        info!("✅ WebSocket connected successfully");

        // Update stats and state
        {
            let mut stats = self.stats.lock().await;
            stats.connections_established += 1;
            stats.last_connection_time = Some(connection_start);
        }

        {
            let mut state = self.connection_state.write().await;
            *state = ConnectionState::Connected;
        }

        let (mut ws_sender, mut ws_receiver) = ws_stream.split();

        // Send subscription message
        let subscription = SubscriptionMessage {
            message_type: "subscribe".to_string(),
            timestamp: Utc::now(),
            data: SubscriptionData {
                action: "subscribe".to_string(),
                subscription: "whatsapp".to_string(),
            },
        };

        let subscription_json = serde_json::to_string(&subscription).map_err(BridgeError::Json)?;

        ws_sender
            .send(Message::Text(subscription_json))
            .await
            .map_err(BridgeError::WebSocket)?;

        info!("📡 Subscribed to WhatsApp messages");

        // Update stats
        {
            let mut stats = self.stats.lock().await;
            stats.messages_sent += 1;
        }

        // Set up heartbeat and shutdown receivers
        let mut heartbeat_interval =
            interval(Duration::from_secs(self.config.heartbeat_interval_secs));
        let mut shutdown_rx = self.shutdown_tx.subscribe();

        // Main message loop
        loop {
            tokio::select! {
                // Handle incoming messages
                message = ws_receiver.next() => {
                    match message {
                        Some(Ok(Message::Text(text))) => {
                            if let Err(e) = self.handle_text_message(&text).await {
                                error!("Failed to handle text message: {}", e);
                            }
                        }
                        Some(Ok(Message::Close(_))) => {
                            info!("WebSocket connection closed by server");
                            break;
                        }
                        Some(Err(e)) => {
                            error!("WebSocket error: {}", e);
                            return Err(Box::new(BridgeError::WebSocket(e)));
                        }
                        None => {
                            warn!("WebSocket stream ended");
                            break;
                        }
                        _ => {
                            debug!("Received non-text WebSocket message");
                        }
                    }
                }

                // Send heartbeat
                _ = heartbeat_interval.tick() => {
                    if let Err(e) = self.send_heartbeat(&mut ws_sender).await {
                        error!("Failed to send heartbeat: {}", e);
                        return Err(e);
                    }
                }

                // Handle shutdown signal
                _ = shutdown_rx.recv() => {
                    info!("Received shutdown signal, closing WebSocket connection");
                    break;
                }
            }
        }

        // Update connection uptime
        {
            let mut stats = self.stats.lock().await;
            stats.connection_uptime_secs += connection_start.elapsed().as_secs();
        }

        Ok(())
    }

    /// Handle incoming text message
    async fn handle_text_message(&self, text: &str) -> BridgeResult<()> {
        debug!("📨 Received WebSocket message: {}", text);

        // Update stats
        {
            let mut stats = self.stats.lock().await;
            stats.messages_received += 1;
            stats.last_message_time = Some(Instant::now());
        }

        match serde_json::from_str::<WebSocketMessage>(text) {
            Ok(message) => {
                // Handle specific message types
                match &message {
                    WebSocketMessage::IncomingWhatsApp { data, .. } => {
                        info!(
                            "📱 Received WhatsApp message from {}: {}",
                            data.from,
                            data.message.chars().take(50).collect::<String>()
                        );
                    }
                    WebSocketMessage::Welcome { data, .. } => {
                        info!(
                            "👋 Received welcome message - Client ID: {}",
                            data.client_id
                        );
                    }
                    WebSocketMessage::StatusUpdate { data, .. } => {
                        debug!("📊 Status update: {} is {}", data.service, data.status);
                    }
                    WebSocketMessage::Error { error, .. } => {
                        warn!("❌ Received error message: {}", error);
                        let mut stats = self.stats.lock().await;
                        stats.errors_encountered += 1;
                    }
                    WebSocketMessage::Ping { .. } => {
                        debug!("🏓 Received ping");
                        // Pong will be sent automatically by the server
                    }
                    WebSocketMessage::Pong { .. } => {
                        debug!("🏓 Received pong");
                        let mut stats = self.stats.lock().await;
                        stats.heartbeats_received += 1;
                    }
                }

                // Broadcast message to subscribers
                if let Err(e) = self.message_tx.send(message) {
                    warn!("Failed to broadcast message to subscribers: {}", e);
                }
            }
            Err(e) => {
                warn!("Failed to parse WebSocket message: {} - Raw: {}", e, text);
                let mut stats = self.stats.lock().await;
                stats.errors_encountered += 1;
            }
        }

        Ok(())
    }

    /// Send heartbeat message
    async fn send_heartbeat(
        &self,
        ws_sender: &mut futures_util::stream::SplitSink<
            tokio_tungstenite::WebSocketStream<
                tokio_tungstenite::MaybeTlsStream<tokio::net::TcpStream>,
            >,
            Message,
        >,
    ) -> BridgeResult<()> {
        debug!("💓 Sending heartbeat");

        let heartbeat = json!({
            "type": "heartbeat",
            "timestamp": Utc::now()
        });

        ws_sender
            .send(Message::Text(heartbeat.to_string()))
            .await
            .map_err(BridgeError::WebSocket)?;

        // Update stats
        {
            let mut stats = self.stats.lock().await;
            stats.messages_sent += 1;
            stats.heartbeats_sent += 1;
        }

        Ok(())
    }

    /// Send a custom message through the WebSocket
    pub async fn send_message(&self, message: serde_json::Value) -> BridgeResult<()> {
        // This would require maintaining a reference to the sender
        // For now, we'll return an error indicating this feature needs implementation
        Err(Box::new(BridgeError::ServiceUnavailable(
            "Direct message sending not yet implemented".to_string(),
        )))
    }

    /// Get connection health status
    pub async fn is_healthy(&self) -> bool {
        let state = self.connection_state().await;
        matches!(state, ConnectionState::Connected)
    }

    /// Get detailed connection metrics
    pub async fn get_connection_metrics(&self) -> ConnectionMetrics {
        let stats = self.get_stats().await;
        let state = self.connection_state().await;

        ConnectionMetrics {
            state,
            uptime_secs: stats.connection_uptime_secs,
            messages_per_second: if stats.connection_uptime_secs > 0 {
                (stats.messages_received + stats.messages_sent) as f64
                    / stats.connection_uptime_secs as f64
            } else {
                0.0
            },
            error_rate: if stats.messages_received > 0 {
                stats.errors_encountered as f64 / stats.messages_received as f64
            } else {
                0.0
            },
            last_activity: stats.last_message_time,
        }
    }
}

/// Connection metrics for monitoring
#[derive(Debug, Clone)]
pub struct ConnectionMetrics {
    pub state: ConnectionState,
    pub uptime_secs: u64,
    pub messages_per_second: f64,
    pub error_rate: f64,
    pub last_activity: Option<Instant>,
}
