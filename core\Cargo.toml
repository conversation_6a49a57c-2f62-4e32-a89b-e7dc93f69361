[workspace]
members = [".", "db/*"]
resolver = "2"

[package]
name = "wellbot-bridge"
version = "0.1.0"
edition = "2024"

[dependencies]

# Async runtime
tokio = { workspace = true, features = ["full"] }
tokio-tungstenite = { workspace = true, features = ["native-tls"] }

# Configuration
config = { workspace = true }
dotenvy = { workspace = true }
toml = { workspace = true }

# Error handling
anyhow = { workspace = true }
thiserror = { workspace = true }

# HTTP client
reqwest = { workspace = true, features = ["json", "rustls-tls"] }

# Logging
tracing = { workspace = true }
tracing-subscriber = { workspace = true, features = ["env-filter", "json"] }
tracing-appender = { workspace = true }

# Rate limiting and backoff
governor = { workspace = true }
backoff = { workspace = true }

# Serialization
serde = { workspace = true, features = ["derive"] }
serde_json = { workspace = true }

# Utilities
chrono = { workspace = true, features = ["serde"] }
url = { workspace = true }
uuid = { workspace = true, features = ["v7", "serde"] }
futures-util = { workspace = true }

# CLI utilities (for examples)
cliclack = { version = "0.3.6", optional = true }
console = { version = "0.16.0", optional = true }
ctrlc = { version = "3.4", optional = true }

[workspace.dependencies]
anyhow = "1.0"
argon2 = "0.5.3"
async-recursion = "1.1.1"
async-trait = "0.1"
backoff = "0.4"
chrono = "0.4.40"
config = "0.15.11"
derive-getters = "0.5"
dotenv = "0.15.0"
dotenvy = "0.15.7"
futures-util = "0.3"
governor = "0.10.0"
jsonwebtoken = "9.3.1"
pretty_assertions = "1.4.1"
reqwest = "0.12.20"
rust_decimal = "1.37.1"
sea-orm = "1"
sea-orm-migration = "1"
serde = "1.0"
serde_json = "1.0"
thiserror = "2"
tokio = "1.45.1"
tokio-tungstenite = "0.27.0"
toml = "0.8.8"
tracing = "0.1"
tracing-appender = "0.2.3"
tracing-subscriber = "0.3"
typed-builder = "0.21.0"
url = "2.5.4"
uuid = "1.17.0"

db_entity = { path = "db/entity" }
db_migration = { path = "db/migration" }
db_service = { path = "db/service" }

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true

# Example features
[features]
default = ["examples"]
examples = ["cliclack", "console", "ctrlc"]

# Examples
[[example]]
name = "ai_client_demo"
required-features = ["examples"]

[[example]]
name = "chat_port_client_demo"
required-features = ["examples"]
