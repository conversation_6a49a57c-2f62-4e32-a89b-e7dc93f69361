package errors

import (
	"errors"
	"strings"
	"testing"
)

func TestAppError_Error(t *testing.T) {
	tests := []struct {
		name     string
		appError *AppError
		expected string
	}{
		{
			name: "error without cause",
			appError: &AppError{
				Type:    ErrorTypeValidation,
				Message: "invalid input",
			},
			expected: "validation: invalid input",
		},
		{
			name: "error with cause",
			appError: &AppError{
				Type:    ErrorTypeWhatsApp,
				Message: "connection failed",
				Cause:   errors.New("network timeout"),
			},
			expected: "whatsapp: connection failed (caused by: network timeout)",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.appError.Error()
			if result != tt.expected {
				t.<PERSON>rf("Expected %q, got %q", tt.expected, result)
			}
		})
	}
}

func TestAppError_Unwrap(t *testing.T) {
	cause := errors.New("original error")
	appError := &AppError{
		Type:    ErrorTypeInternal,
		Message: "wrapper error",
		Cause:   cause,
	}

	unwrapped := appError.Unwrap()
	if unwrapped != cause {
		t.Errorf("Expected unwrapped error to be %v, got %v", cause, unwrapped)
	}

	// Test error without cause
	appErrorNoCause := &AppError{
		Type:    ErrorTypeInternal,
		Message: "no cause error",
	}

	unwrappedNoCause := appErrorNoCause.Unwrap()
	if unwrappedNoCause != nil {
		t.Errorf("Expected unwrapped error to be nil, got %v", unwrappedNoCause)
	}
}

func TestAppError_WithContext(t *testing.T) {
	appError := &AppError{
		Type:    ErrorTypeHTTP,
		Message: "request failed",
	}

	result := appError.WithContext("status_code", 404)

	if result.Context == nil {
		t.Fatal("Expected context to be initialized")
	}

	if result.Context["status_code"] != 404 {
		t.Errorf("Expected status_code to be 404, got %v", result.Context["status_code"])
	}

	// Test adding multiple context values
	result.WithContext("method", "GET").WithContext("url", "/api/test")

	if len(result.Context) != 3 {
		t.Errorf("Expected 3 context values, got %d", len(result.Context))
	}
}

func TestAppError_WithStackTrace(t *testing.T) {
	appError := &AppError{
		Type:    ErrorTypeInternal,
		Message: "internal error",
	}

	result := appError.WithStackTrace()

	if result.StackTrace == "" {
		t.Error("Expected stack trace to be present")
	}

	// Stack trace should contain function names
	if !strings.Contains(result.StackTrace, "TestAppError_WithStackTrace") {
		t.Error("Expected stack trace to contain test function name")
	}
}

func TestNew(t *testing.T) {
	appError := New(ErrorTypeValidation, "test message")

	if appError.Type != ErrorTypeValidation {
		t.Errorf("Expected type %s, got %s", ErrorTypeValidation, appError.Type)
	}

	if appError.Message != "test message" {
		t.Errorf("Expected message 'test message', got %q", appError.Message)
	}

	if appError.Cause != nil {
		t.Errorf("Expected cause to be nil, got %v", appError.Cause)
	}
}

func TestNewf(t *testing.T) {
	appError := Newf(ErrorTypeAIService, "error code: %d, message: %s", 500, "server error")

	expectedMessage := "error code: 500, message: server error"
	if appError.Message != expectedMessage {
		t.Errorf("Expected message %q, got %q", expectedMessage, appError.Message)
	}
}

func TestWrap(t *testing.T) {
	originalError := errors.New("original error")
	appError := Wrap(originalError, ErrorTypeWhatsApp, "wrapped error")

	if appError.Type != ErrorTypeWhatsApp {
		t.Errorf("Expected type %s, got %s", ErrorTypeWhatsApp, appError.Type)
	}

	if appError.Message != "wrapped error" {
		t.Errorf("Expected message 'wrapped error', got %q", appError.Message)
	}

	if appError.Cause != originalError {
		t.Errorf("Expected cause to be %v, got %v", originalError, appError.Cause)
	}
}

func TestWrapf(t *testing.T) {
	originalError := errors.New("original error")
	appError := Wrapf(originalError, ErrorTypeConfig, "config error at line %d", 42)

	expectedMessage := "config error at line 42"
	if appError.Message != expectedMessage {
		t.Errorf("Expected message %q, got %q", expectedMessage, appError.Message)
	}

	if appError.Cause != originalError {
		t.Errorf("Expected cause to be %v, got %v", originalError, appError.Cause)
	}
}

func TestConvenienceFunctions(t *testing.T) {
	tests := []struct {
		name         string
		createError  func() *AppError
		expectedType ErrorType
	}{
		{
			name:         "NewValidationError",
			createError:  func() *AppError { return NewValidationError("validation failed") },
			expectedType: ErrorTypeValidation,
		},
		{
			name:         "NewValidationErrorf",
			createError:  func() *AppError { return NewValidationErrorf("field %s is required", "email") },
			expectedType: ErrorTypeValidation,
		},
		{
			name:         "NewWhatsAppError",
			createError:  func() *AppError { return NewWhatsAppError("connection failed") },
			expectedType: ErrorTypeWhatsApp,
		},
		{
			name:         "NewWhatsAppErrorf",
			createError:  func() *AppError { return NewWhatsAppErrorf("timeout after %d seconds", 30) },
			expectedType: ErrorTypeWhatsApp,
		},
		{
			name:         "NewAIServiceError",
			createError:  func() *AppError { return NewAIServiceError("service unavailable") },
			expectedType: ErrorTypeAIService,
		},
		{
			name:         "NewAIServiceErrorf",
			createError:  func() *AppError { return NewAIServiceErrorf("status code: %d", 500) },
			expectedType: ErrorTypeAIService,
		},
		{
			name:         "NewHTTPError",
			createError:  func() *AppError { return NewHTTPError("bad request") },
			expectedType: ErrorTypeHTTP,
		},
		{
			name:         "NewHTTPErrorf",
			createError:  func() *AppError { return NewHTTPErrorf("method %s not allowed", "POST") },
			expectedType: ErrorTypeHTTP,
		},
		{
			name:         "NewConfigError",
			createError:  func() *AppError { return NewConfigError("invalid config") },
			expectedType: ErrorTypeConfig,
		},
		{
			name:         "NewConfigErrorf",
			createError:  func() *AppError { return NewConfigErrorf("missing key: %s", "api_key") },
			expectedType: ErrorTypeConfig,
		},
		{
			name:         "NewInternalError",
			createError:  func() *AppError { return NewInternalError("internal error") },
			expectedType: ErrorTypeInternal,
		},
		{
			name:         "NewInternalErrorf",
			createError:  func() *AppError { return NewInternalErrorf("panic: %v", "nil pointer") },
			expectedType: ErrorTypeInternal,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			appError := tt.createError()

			if appError.Type != tt.expectedType {
				t.Errorf("Expected type %s, got %s", tt.expectedType, appError.Type)
			}

			if appError.Message == "" {
				t.Error("Expected message to be present")
			}

			// Internal errors should have stack trace
			if tt.expectedType == ErrorTypeInternal && appError.StackTrace == "" {
				t.Error("Expected internal error to have stack trace")
			}
		})
	}
}

func TestWrapConvenienceFunctions(t *testing.T) {
	originalError := errors.New("original error")

	tests := []struct {
		name         string
		createError  func() *AppError
		expectedType ErrorType
	}{
		{
			name:         "WrapWhatsAppError",
			createError:  func() *AppError { return WrapWhatsAppError(originalError, "whatsapp error") },
			expectedType: ErrorTypeWhatsApp,
		},
		{
			name:         "WrapAIServiceError",
			createError:  func() *AppError { return WrapAIServiceError(originalError, "ai service error") },
			expectedType: ErrorTypeAIService,
		},
		{
			name:         "WrapConfigError",
			createError:  func() *AppError { return WrapConfigError(originalError, "config error") },
			expectedType: ErrorTypeConfig,
		},
		{
			name:         "WrapInternalError",
			createError:  func() *AppError { return WrapInternalError(originalError, "internal error") },
			expectedType: ErrorTypeInternal,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			appError := tt.createError()

			if appError.Type != tt.expectedType {
				t.Errorf("Expected type %s, got %s", tt.expectedType, appError.Type)
			}

			if appError.Cause != originalError {
				t.Errorf("Expected cause to be %v, got %v", originalError, appError.Cause)
			}

			// Internal errors should have stack trace
			if tt.expectedType == ErrorTypeInternal && appError.StackTrace == "" {
				t.Error("Expected internal error to have stack trace")
			}
		})
	}
}

func TestIsType(t *testing.T) {
	appError := NewValidationError("validation error")
	regularError := errors.New("regular error")

	if !IsType(appError, ErrorTypeValidation) {
		t.Error("Expected IsType to return true for matching AppError type")
	}

	if IsType(appError, ErrorTypeWhatsApp) {
		t.Error("Expected IsType to return false for non-matching AppError type")
	}

	if IsType(regularError, ErrorTypeValidation) {
		t.Error("Expected IsType to return false for regular error")
	}
}

func TestGetType(t *testing.T) {
	appError := NewWhatsAppError("whatsapp error")
	regularError := errors.New("regular error")

	if GetType(appError) != ErrorTypeWhatsApp {
		t.Errorf("Expected GetType to return %s, got %s", ErrorTypeWhatsApp, GetType(appError))
	}

	if GetType(regularError) != ErrorTypeInternal {
		t.Errorf("Expected GetType to return %s for regular error, got %s", ErrorTypeInternal, GetType(regularError))
	}
}

func TestRecovery(t *testing.T) {
	// Test normal case (no panic)
	result := Recovery()
	if result != nil {
		t.Errorf("Expected Recovery to return nil when no panic, got %v", result)
	}

	// Test panic case - we need to simulate what happens in a real panic scenario
	t.Run("panic_recovery", func(t *testing.T) {
		// We can't easily test the actual recover() function in isolation
		// since it only works within a defer statement during a panic.
		// Instead, we'll test the error creation logic directly.

		// Simulate what Recovery() would create during a panic
		panicValue := "test panic"
		expectedError := NewInternalErrorf("panic recovered: %v", panicValue)

		if expectedError.Type != ErrorTypeInternal {
			t.Errorf("Expected error type %s, got %s", ErrorTypeInternal, expectedError.Type)
		}

		if !strings.Contains(expectedError.Message, "panic recovered") {
			t.Error("Expected error message to contain 'panic recovered'")
		}

		if !strings.Contains(expectedError.Message, "test panic") {
			t.Error("Expected error message to contain panic value")
		}

		if expectedError.StackTrace == "" {
			t.Error("Expected stack trace to be present for internal error")
		}
	})
}
