/*!
# Error Types

Comprehensive error handling for the Wellbot Bridge Service.
*/

use thiserror::Error;

/// Main error type for the bridge service
#[derive(Error, Debug)]
pub enum BridgeError {
    #[error("WebSocket connection error: {0}")]
    WebSocket(#[from] tokio_tungstenite::tungstenite::Error),

    #[error("JSON serialization error: {0}")]
    Json(#[from] serde_json::Error),

    #[error("HTTP client error: {0}")]
    Http(#[from] reqwest::Error),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Chat-port service error: {message}")]
    ChatPort { message: String },

    #[error("AI service error: {message}, status: {status:?}")]
    AiService {
        message: String,
        status: Option<reqwest::StatusCode>,
    },

    #[error("Authentication error: {0}")]
    Authentication(String),

    #[error("Rate limit exceeded: {0}")]
    RateLimit(String),

    #[error("Service unavailable: {0}")]
    ServiceUnavailable(String),

    #[error("Serialization error: {0}")]
    SerializationError(String),

    #[error("Timeout error: {0}")]
    Timeout(String),

    #[error("Invalid message format: {0}")]
    InvalidMessage(String),
}

/// Result type alias for bridge operations
pub type BridgeResult<T> = Result<T, Box<BridgeError>>;
