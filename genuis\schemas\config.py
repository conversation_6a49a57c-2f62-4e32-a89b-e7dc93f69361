from pydantic import BaseModel, Field, field_validator


class AIConfig(BaseModel):
    """AI service configuration model"""

    model: str = "gemini-2.5-flash"
    temperature: float = Field(default=0.7, ge=0, le=1)
    max_tokens: int = Field(default=4000, gt=0)
    system_prompt: str = Field(default="You are a helpful AI assistant.")

    @field_validator("temperature")
    def validate_temperature(cls, v):
        if not 0 <= v <= 1:
            raise ValueError("Temperature must be between 0 and 1")
        return v
