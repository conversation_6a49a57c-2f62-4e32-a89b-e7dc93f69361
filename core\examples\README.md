# 🤖 Wellbot AI Client Examples

Beautiful interactive demonstrations of the Wellbot AI Client service using [cliclack](https://github.com/fadeevab/cliclack).

## 🎯 Available Examples

### `ai_client_demo` - Interactive AI Client Demo

A comprehensive, interactive demonstration showcasing all AI client capabilities with a modern CLI interface.

**Features:**
- ✨ Beautiful interactive prompts with colors and emojis
- 🔧 Configuration management (default, custom, environment)
- 🧠 AI explanation generation with customizable parameters
- 🏥 Health monitoring and connection testing
- 📦 Batch processing demonstrations
- 📊 Performance metrics and detailed result display
- 🎯 Error handling with helpful troubleshooting tips
- 🔄 Graceful Ctrl-C handling

## 🚀 Running the Examples

### Prerequisites

1. **AI Service Running**: Make sure the Genuis FastAPI service is running:
   ```bash
   # In the genuis directory
   cd genuis
   python -m uvicorn api.main:app --reload --port 8000
   ```

2. **Environment Setup** (optional):
   ```bash
   # Create .env file in core directory
   AI_SERVICE_URL=http://localhost:8000/api/v1
   AI_SERVICE_API_KEY=wellbot-dev-key-2025
   AI_SERVICE_TIMEOUT=60
   AI_SERVICE_RETRY_ATTEMPTS=3
   AI_SERVICE_RETRY_DELAY=2
   AI_SERVICE_DEFAULT_TEMPERATURE=0.7
   AI_SERVICE_DEFAULT_MAX_TOKENS=4000
   ```

### Running the Demo

```bash
# From the core directory
cargo run --example ai_client_demo --features examples
```

## 🎨 Demo Walkthrough

### 1. Configuration Setup
Choose how to configure the AI service:
- **Default**: Uses built-in development settings
- **Custom**: Interactive configuration with validation
- **Environment**: Loads from environment variables

### 2. Health Check
Verify the AI service is running and accessible.

### 3. Main Menu Options

#### 🧠 Generate AI Explanation
- Choose from predefined prompts or enter custom ones
- Configure advanced parameters (temperature, max tokens)
- View beautifully formatted results with metadata
- Support for multiple prompts in sequence

#### 🔬 Test Connection
- Verify connectivity to the AI service
- Get helpful troubleshooting tips on failure

#### 📦 Batch Processing Demo
- Process multiple prompts automatically
- View real-time progress and statistics
- Performance metrics and success/failure rates

#### ⚙️ View Configuration
- Display current AI service settings
- Formatted with colors and clear organization

#### 🏥 Health Check
- Check AI service health status
- Monitor service availability

## 🎭 Visual Features

The demo uses cliclack's beautiful UI components:

- **Intro/Outro**: Styled headers and footers
- **Spinners**: Animated loading indicators
- **Progress**: Real-time operation feedback
- **Colors**: Semantic color coding (green=success, red=error, etc.)
- **Emojis**: Visual icons for better UX
- **Notes**: Formatted information blocks
- **Logs**: Categorized messages (info, warning, error, success)

## 🔧 Customization

### Adding New Prompts

Edit the `generate_explanation_demo` function to add new predefined prompts:

```rust
.item("new_topic", "New Topic", "Description of the new topic")
```

### Modifying Configuration

Update the `get_default_config` function to change default settings:

```rust
Config {
    ai_service: AiServiceConfig {
        base_url: "your_custom_url".to_string(),
        // ... other settings
    },
}
```

### Error Handling

The demo includes comprehensive error handling with user-friendly messages and troubleshooting tips. Error types are matched and appropriate guidance is provided.

## 🐛 Troubleshooting

### Common Issues

1. **"Connection refused"**
   - Ensure the Genuis FastAPI service is running on port 8000
   - Check if the base URL is correct

2. **"Authentication failed"**
   - Verify the API key matches the service configuration
   - Default key: `wellbot-dev-key-2025`

3. **"Service unavailable"**
   - The AI service might be temporarily down
   - Check service logs for errors

4. **"Rate limit exceeded"**
   - Wait a moment before making more requests
   - Consider increasing delays in batch processing

### Debug Mode

Run with debug logging to see detailed information:

```bash
RUST_LOG=debug cargo run --example ai_client_demo --features examples
```

## 📚 Dependencies

The example uses these additional dependencies (enabled with `examples` feature):

- `cliclack`: Beautiful CLI prompts and UI components
- `console`: Terminal styling and colors
- `ctrlc`: Graceful Ctrl-C handling

## 🤝 Contributing

To add new examples or improve existing ones:

1. Create new example files in `core/examples/`
2. Add the example to `Cargo.toml` under `[[example]]`
3. Update this README with documentation
4. Follow the existing code style and patterns

## 📄 License

This example code follows the same license as the main Wellbot project.
