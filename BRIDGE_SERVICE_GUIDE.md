# 🌉 Wellbot Bridge Service - Comprehensive Guide

A professional Rust bridge service that connects WhatsApp messaging with AI processing, featuring JID authorization, real-time WebSocket communication, and comprehensive monitoring.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WhatsApp      │───▶│  Bridge Service │───▶│   AI Service    │
│   Messages      │    │                 │    │   (Genuis)      │
│                 │◄───│ • JID Filter    │◄───│                 │
│                 │    │ • WebSocket     │    │                 │
│                 │    │ • Processing    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Core Components

1. **🌉 Bridge Service** - Main orchestrator that coordinates all components
2. **🔐 JID Authorization** - Manages authorized WhatsApp accounts with persistence
3. **🔌 WebSocket Manager** - Enhanced real-time communication with connection pooling
4. **🤖 AI Integration** - Seamless integration with AI services for response generation
5. **📊 Message Processor** - Intelligent message routing and processing
6. **🎯 CLI Interface** - Beautiful interactive management interface

## 🚀 Quick Start

### Prerequisites

- Rust 1.70+ with Cargo
- Go chat-port service running at `ws://localhost:8081/ws`
- AI service (genuis) running at `http://localhost:8000`

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd wellbot-bridge

# Build the project
cargo build --release

# Run the interactive CLI
cargo run --bin wellbot-cli --features examples
```

### Basic Usage

```rust
use wellbot_bridge::{
    config::Config,
    services::bridge_service::{BridgeService, BridgeServiceConfig},
};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Load configuration
    let config = Config::from_env()?;
    
    // Create bridge service
    let bridge_config = BridgeServiceConfig {
        allowed_jids: vec!["<EMAIL>".to_string()],
        enable_background_processing: true,
        websocket_auto_reconnect: true,
        ai_processing_enabled: true,
        max_concurrent_ai_requests: 5,
    };
    
    let bridge_service = BridgeService::new_with_runtime_config(
        config, 
        bridge_config
    ).await?;
    
    // Start the service
    bridge_service.start().await?;
    
    // Your application logic here...
    
    // Graceful shutdown
    bridge_service.stop().await?;
    Ok(())
}
```

## 🔐 JID Authorization System

The JID (WhatsApp account identifier) authorization system provides secure access control with persistent storage and dynamic management.

### Features

- ✅ Persistent storage with JSON serialization
- ✅ Dynamic JID list updates
- ✅ Activity tracking and statistics
- ✅ Automatic cleanup of inactive accounts
- ✅ Real-time authorization checking

### Usage

```rust
use wellbot_bridge::services::jid_authorization::{
    JidAuthorizationService, 
    JidAuthConfig
};

// Initialize JID service
let jid_config = JidAuthConfig::default();
let jid_service = JidAuthorizationService::new(jid_config).await?;

// Add authorized JID
jid_service.add_jid(
    "<EMAIL>".to_string(),
    Some("John Doe".to_string())
).await?;

// Check authorization
let is_authorized = jid_service.is_authorized("<EMAIL>").await;

// Get statistics
let stats = jid_service.get_stats().await;
println!("Total JIDs: {}, Active: {}", stats.total_jids, stats.active_jids);
```

## 🔌 Enhanced WebSocket Communication

The WebSocket manager provides robust real-time communication with advanced features:

### Features

- ✅ Connection pooling and management
- ✅ Automatic reconnection with exponential backoff
- ✅ Heartbeat mechanism for connection health
- ✅ Comprehensive error handling and recovery
- ✅ Real-time statistics and monitoring
- ✅ JSON message protocol support

### Configuration

```rust
use wellbot_bridge::services::websocket_manager::WebSocketManager;

let websocket_manager = WebSocketManager::new(chat_port_config);

// Start WebSocket connection
websocket_manager.start().await?;

// Subscribe to messages
let mut message_rx = websocket_manager.subscribe_messages();

// Handle messages
while let Ok(message) = message_rx.recv().await {
    match message {
        WebSocketMessage::IncomingWhatsApp { data, .. } => {
            println!("Received WhatsApp message: {}", data.message);
        }
        _ => {}
    }
}
```

## 🤖 AI Integration

Seamless integration with AI services for intelligent response generation.

### Features

- ✅ Async AI request processing
- ✅ Timeout handling and error recovery
- ✅ Fallback response mechanisms
- ✅ Request/response statistics
- ✅ Configurable AI parameters

### Message Processing Flow

```
1. WhatsApp Message Received
2. JID Authorization Check
3. AI Request Generation
4. AI Service Call (with timeout)
5. Response Processing
6. WhatsApp Response Delivery
```

## 📊 Monitoring and Statistics

Comprehensive monitoring with real-time statistics and health checks.

### Available Metrics

- **Message Processing**: Total, authorized, unauthorized messages
- **AI Integration**: Requests sent, responses received, errors
- **WebSocket**: Connections, reconnections, heartbeats
- **JID Authorization**: Active JIDs, message activity, cleanup stats
- **Service Health**: Component status, uptime, error rates

### Health Checks

```rust
// Check overall service health
let is_healthy = bridge_service.health_check().await?;

// Get detailed statistics
let stats = bridge_service.get_stats().await;
println!("Messages processed: {}", stats.messages_processed);
println!("AI requests: {}", stats.ai_requests_sent);
```

## 🎯 Interactive CLI

Beautiful command-line interface built with cliclack for comprehensive service management.

### Features

- ✅ Interactive service configuration
- ✅ JID management with real-time updates
- ✅ Service control (start/stop/restart)
- ✅ Real-time monitoring dashboard
- ✅ Connection testing and diagnostics
- ✅ Interactive demonstrations

### CLI Commands

```bash
# Run interactive CLI
cargo run --bin wellbot-cli --features examples

# Available operations:
# - ⚙️  Configure Service
# - 🔐 Manage JIDs  
# - 🚀 Service Control
# - 📊 Real-time Monitoring
# - 🎯 Interactive Demo
```

## 🧪 Testing and Examples

### Running Examples

```bash
# Complete bridge service demonstration
cargo run --example bridge_service_demo --features examples

# AI client demo
cargo run --example ai_client_demo --features examples

# Chat-port client demo  
cargo run --example chat_port_client_demo --features examples
```

### Test Coverage

Run comprehensive tests:

```bash
# Run all tests
cargo test

# Run with coverage
cargo test --features test-coverage

# Run specific test suites
cargo test bridge_service
cargo test jid_authorization
cargo test websocket_manager
```

## ⚙️ Configuration

### Environment Variables

```bash
# AI Service Configuration
AI_SERVICE_BASE_URL=http://localhost:8000
AI_SERVICE_TIMEOUT_SECS=30

# Chat-Port Configuration  
CHAT_PORT_API_BASE_URL=http://localhost:8081
CHAT_PORT_WEBSOCKET_URL=ws://localhost:8081/ws
CHAT_PORT_CONNECTION_TIMEOUT_SECS=10
CHAT_PORT_MAX_RECONNECT_ATTEMPTS=5
CHAT_PORT_RECONNECT_DELAY_SECS=5
CHAT_PORT_HEARTBEAT_INTERVAL_SECS=30

# Bridge Configuration
BRIDGE_MAX_CONCURRENT_REQUESTS=10
BRIDGE_REQUEST_TIMEOUT_SECS=60
BRIDGE_RATE_LIMIT_PER_MINUTE=100

# Logging
LOG_LEVEL=info
LOG_FORMAT=pretty
```

### Configuration File

Create `config.toml`:

```toml
[ai_service]
base_url = "http://localhost:8000"
timeout_secs = 30

[chat_port]
api_base_url = "http://localhost:8081"
websocket_url = "ws://localhost:8081/ws"
connection_timeout_secs = 10
max_reconnect_attempts = 5
reconnect_delay_secs = 5
heartbeat_interval_secs = 30

[bridge]
max_concurrent_requests = 10
request_timeout_secs = 60
rate_limit_per_minute = 100
```

## 🔧 Advanced Usage

### Custom Message Handlers

```rust
// Create custom message processor
let message_processor = MessageProcessor::new_with_ai_client(
    bridge_config,
    chat_port_client,
    Some(ai_client),
    allowed_jids,
);

// Process messages with custom logic
let message = IncomingWhatsAppData { /* ... */ };
message_processor.process_message(message).await?;
```

### WebSocket Event Handling

```rust
// Subscribe to WebSocket events
let mut message_rx = chat_port_client.subscribe_to_messages();

while let Ok(message) = message_rx.recv().await {
    match message {
        WebSocketMessage::IncomingWhatsApp { data, .. } => {
            // Handle WhatsApp message
        }
        WebSocketMessage::StatusUpdate { data, .. } => {
            // Handle status updates
        }
        WebSocketMessage::Error { error, .. } => {
            // Handle errors
        }
        _ => {}
    }
}
```

## 🚨 Error Handling

The bridge service provides comprehensive error handling with custom error types:

```rust
use wellbot_bridge::error::{BridgeError, BridgeResult};

match bridge_service.process_whatsapp_message(message).await {
    Ok(_) => println!("Message processed successfully"),
    Err(BridgeError::Authentication(msg)) => {
        println!("Authorization failed: {}", msg);
    }
    Err(BridgeError::Timeout(msg)) => {
        println!("Request timeout: {}", msg);
    }
    Err(BridgeError::ServiceUnavailable(msg)) => {
        println!("Service unavailable: {}", msg);
    }
    Err(e) => println!("Other error: {}", e),
}
```

## 📈 Performance Considerations

- **Concurrent Processing**: Configurable limits for AI requests
- **Rate Limiting**: Built-in rate limiting for message processing
- **Connection Pooling**: Efficient WebSocket connection management
- **Memory Management**: Automatic cleanup of inactive JIDs
- **Async Processing**: Full async/await support throughout

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Update documentation
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
