# WebSocket Real-time Communication

The ChatPort WhatsApp service includes comprehensive WebSocket functionality for real-time communication between the Go service and clients. This enables live monitoring, message streaming, and status updates.

## Features

### 🔌 **WebSocket Server**
- WebSocket endpoint at `/ws` with HTTP upgrade support
- Built on gorilla/websocket library for robust WebSocket handling
- Configurable connection limits and buffer sizes
- CORS support for cross-origin requests

### 📡 **Real-time Broadcasting**
- **Incoming WhatsApp Messages**: Live stream of received WhatsApp messages
- **Service Status Updates**: Real-time connection status changes (connected/disconnected/reconnecting)
- **Health Status Changes**: Service health monitoring with status transitions
- **Metrics Updates**: Live performance metrics and success rates

### 👥 **Connection Management**
- Connection pool managing multiple concurrent WebSocket clients
- Graceful connection/disconnection handling
- Heartbeat/ping-pong mechanism for connection health
- Automatic cleanup of inactive connections
- Configurable rate limiting and connection limits

### 📨 **Message Protocol**
- JSON-based message format with typed events
- Client subscription system for selective message filtering
- Error handling for malformed messages
- Message routing based on subscription preferences

## Configuration

WebSocket functionality is configured through environment variables:

```bash
# Enable/disable WebSocket functionality
WEBSOCKET_ENABLED=true

# Connection limits
WEBSOCKET_MAX_CLIENTS=1000
WEBSOCKET_CLIENT_TIMEOUT=5m
WEBSOCKET_CLEANUP_INTERVAL=30s

# Buffer sizes
WEBSOCKET_READ_BUFFER_SIZE=1024
WEBSOCKET_WRITE_BUFFER_SIZE=1024
WEBSOCKET_BROADCAST_BUFFER_SIZE=1000

# Features
WEBSOCKET_ENABLE_COMPRESSION=true
WEBSOCKET_METRICS_INTERVAL=10s

# Security
WEBSOCKET_ENABLE_AUTH=false
WEBSOCKET_RATE_LIMIT=10
WEBSOCKET_RATE_LIMIT_WINDOW=1m
```

## API Endpoints

### WebSocket Connection
```
GET /ws
Upgrade: websocket
Connection: Upgrade
```

### WebSocket Statistics
```
GET /api/ws/stats
```

Returns hub statistics, client information, and configuration details.

## Message Types

### Incoming Messages (Server → Client)

#### 1. Welcome Message
```json
{
  "type": "welcome",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "client_id": "client-abc123",
    "server_time": "2024-01-15T10:30:00Z",
    "version": "1.0.0",
    "capabilities": ["whatsapp", "status", "metrics", "health"]
  }
}
```

#### 2. Incoming WhatsApp Message
```json
{
  "type": "incoming_whatsapp",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "from": "<EMAIL>",
    "message": "Hello, World!",
    "timestamp": "2024-01-15T10:30:00Z",
    "message_id": "msg_abc123"
  }
}
```

#### 3. Status Update
```json
{
  "type": "status_update",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "service": "whatsapp",
    "status": "connected",
    "details": "Connection established successfully",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

#### 4. Metrics Update
```json
{
  "type": "metrics_update",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "messages_received": 150,
    "messages_sent": 145,
    "messages_failed": 5,
    "message_success_rate": 96.7,
    "ai_service_calls": 120,
    "ai_service_success_rate": 98.3,
    "http_requests": 200,
    "http_success_rate": 99.5
  }
}
```

#### 5. Health Update
```json
{
  "type": "health_update",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "status": "ok",
    "previous_status": "degraded",
    "services": {
      "whatsapp": {
        "connected": true,
        "jid": "<EMAIL>"
      }
    }
  }
}
```

#### 6. Error Message
```json
{
  "type": "error",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "code": "INVALID_SUBSCRIPTION",
    "message": "Invalid subscription type",
    "details": "Subscription 'invalid_type' is not supported"
  }
}
```

### Outgoing Messages (Client → Server)

#### 1. Subscribe to Events
```json
{
  "type": "subscribe",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "action": "subscribe",
    "subscription": "whatsapp"
  }
}
```

#### 2. Unsubscribe from Events
```json
{
  "type": "unsubscribe",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "action": "unsubscribe",
    "subscription": "metrics"
  }
}
```

#### 3. Heartbeat
```json
{
  "type": "heartbeat",
  "timestamp": "2024-01-15T10:30:00Z",
  "data": {
    "client_id": "client-abc123",
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

## Subscription Types

- **`whatsapp`**: Receive incoming WhatsApp messages
- **`status`**: Receive service status updates
- **`metrics`**: Receive real-time metrics updates
- **`health`**: Receive health status changes
- **`all`**: Receive all message types

## Client Example

### JavaScript WebSocket Client
```javascript
const ws = new WebSocket('ws://localhost:8081/ws');

ws.onopen = function(event) {
    console.log('Connected to ChatPort WebSocket');
    
    // Subscribe to WhatsApp messages
    ws.send(JSON.stringify({
        type: 'subscribe',
        timestamp: new Date().toISOString(),
        data: {
            action: 'subscribe',
            subscription: 'whatsapp'
        }
    }));
};

ws.onmessage = function(event) {
    const message = JSON.parse(event.data);
    
    switch (message.type) {
        case 'welcome':
            console.log('Welcome:', JSON.parse(message.data));
            break;
        case 'incoming_whatsapp':
            const whatsapp = JSON.parse(message.data);
            console.log(`WhatsApp from ${whatsapp.from}: ${whatsapp.message}`);
            break;
        case 'status_update':
            const status = JSON.parse(message.data);
            console.log(`Status: ${status.service} is ${status.status}`);
            break;
    }
};
```

### HTML Client
See `examples/websocket_client.html` for a complete HTML client implementation.

## Integration with Existing Components

### Broadcasting WhatsApp Messages
```go
import "chatport-go/internal/websocket"

// Broadcast incoming WhatsApp message
websocket.BroadcastWhatsApp(from, message, messageID)
```

### Broadcasting Status Updates
```go
// Broadcast service status change
websocket.BroadcastStatus("whatsapp", "connected", "Connection established")
```

### Broadcasting Health Changes
```go
// Broadcast health status change
services := map[string]interface{}{
    "whatsapp": map[string]interface{}{
        "connected": true,
        "jid": "<EMAIL>",
    },
}
websocket.BroadcastHealth("ok", services)
```

## Security Considerations

1. **CORS Configuration**: Configure allowed origins in production
2. **Rate Limiting**: Implement connection rate limiting per IP
3. **Authentication**: Enable WebSocket authentication for sensitive environments
4. **Message Validation**: All incoming messages are validated and sanitized
5. **Connection Limits**: Enforce maximum concurrent connections

## Monitoring

### WebSocket Statistics
- Current active connections
- Total connections since startup
- Messages sent/dropped
- Client timeout statistics
- Hub performance metrics

### Health Checks
WebSocket functionality is integrated with the service health check system and reports connection status.

## Testing

Comprehensive test suite covering:
- Message type validation and parsing
- Client connection management
- Hub broadcasting functionality
- Error handling and edge cases
- Concurrent connection scenarios
- Authentication and rate limiting

Run WebSocket tests:
```bash
go test -cover ./internal/websocket
```

## Performance

- **Concurrent Connections**: Supports 1000+ concurrent WebSocket connections
- **Message Throughput**: High-performance message broadcasting with buffered channels
- **Memory Efficiency**: Automatic cleanup of inactive connections
- **CPU Optimization**: Efficient goroutine management for client handling
