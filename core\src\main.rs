use std::sync::Arc;

use anyhow::Result;
use tracing::info;
use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};
use wellbot_bridge::{config::Config, services::bridge_service::BridgeService};

/// Application state for HTTP handlers
#[derive(Debug, Clone)]
struct AppState {
    bridge_service: Arc<BridgeService>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    init_logging().await?;

    // Load configuration
    let config = Config::from_env()?;

    // Initialize the bridge service
    let bridge_service = Arc::new(BridgeService::new(config.clone()).await?);

    // Create application state
    let _app_state = AppState {
        bridge_service: bridge_service.clone(),
    };

    info!(
        "🚀 Starting Wellbot Bridge Service v{}",
        env!("CARGO_PKG_VERSION")
    );

    Ok(())
}

/// Initialize structured logging based on configuration
async fn init_logging() -> Result<()> {
    // Try to load config for logging settings, but use defaults if it fails
    let log_level = std::env::var("LOG_LEVEL").unwrap_or_else(|_| "info".to_string());
    let log_format = std::env::var("LOG_FORMAT").unwrap_or_else(|_| "pretty".to_string());

    let env_filter = format!("wellbot_bridge={},tower_http=debug", log_level);

    let subscriber = tracing_subscriber::registry().with(
        tracing_subscriber::EnvFilter::try_from_default_env().unwrap_or_else(|_| env_filter.into()),
    );

    match log_format.as_str() {
        "json" => {
            subscriber
                .with(tracing_subscriber::fmt::layer().json().with_target(false))
                .init();
        }
        _ => {
            subscriber
                .with(tracing_subscriber::fmt::layer().with_target(false))
                .init();
        }
    }

    info!(
        "📝 Logging initialized - Level: {}, Format: {}",
        log_level, log_format
    );
    Ok(())
}
