use std::{sync::Arc, time::Duration};

use reqwest::Client as HttpClient;
use tracing::{debug, error, info, instrument, warn};

use crate::{
    config::ChatPortConfig,
    error::{BridgeError, BridgeResult},
    services::websocket_manager::{ConnectionState, WebSocketManager},
    types::{IncomingWhatsAppData, SendMessageRequest, SendMessageResponse, WebSocketMessage},
};

/// Chat-port service client with enhanced WebSocket management
#[derive(Debug)]
pub struct ChatPortClient {
    config: ChatPortConfig,
    http_client: HttpClient,
    websocket_manager: Arc<WebSocketManager>,
}

impl ChatPortClient {
    /// Create a new chat-port client with enhanced WebSocket management
    pub fn new(config: ChatPortConfig) -> BridgeResult<Self> {
        let http_client = HttpClient::builder()
            .timeout(Duration::from_secs(30))
            .user_agent(format!("wellbot-bridge/{}", env!("CARGO_PKG_VERSION")))
            .build()
            .map_err(BridgeError::Http)?;

        // Initialize WebSocket manager
        let websocket_manager = Arc::new(WebSocketManager::new(config.clone()));

        info!(
            "Chat-port client initialized - WebSocket: {}, API: {}",
            config.websocket_url, config.api_base_url
        );

        Ok(Self {
            config,
            http_client,
            websocket_manager,
        })
    }

    /// Get the API base URL
    pub fn api_base_url(&self) -> &str {
        &self.config.api_base_url
    }

    /// Start the WebSocket connection
    #[instrument(skip(self))]
    pub async fn start_websocket(&self) -> BridgeResult<()> {
        info!("🚀 Starting WebSocket connection...");
        self.websocket_manager.start().await
    }

    /// Stop the WebSocket connection
    #[instrument(skip(self))]
    pub async fn stop_websocket(&self) -> BridgeResult<()> {
        info!("🛑 Stopping WebSocket connection...");
        self.websocket_manager.stop().await
    }

    /// Get WebSocket connection state
    pub async fn websocket_state(&self) -> ConnectionState {
        self.websocket_manager.connection_state().await
    }

    /// Check if WebSocket is healthy
    pub async fn is_websocket_healthy(&self) -> bool {
        self.websocket_manager.is_healthy().await
    }

    /// Get WebSocket statistics
    pub async fn get_websocket_stats(&self) -> crate::services::websocket_manager::WebSocketStats {
        self.websocket_manager.get_stats().await
    }

    /// Subscribe to WebSocket messages
    pub fn subscribe_to_messages(&self) -> tokio::sync::broadcast::Receiver<WebSocketMessage> {
        self.websocket_manager.subscribe_messages()
    }

    /// Connect to chat-port WebSocket and listen for messages using enhanced WebSocket manager
    #[instrument(skip(self, message_handler))]
    pub async fn listen_for_messages<F, Fut>(&self, mut message_handler: F) -> BridgeResult<()>
    where
        F: FnMut(WebSocketMessage) -> Fut + Send,
        Fut: std::future::Future<Output = ()> + Send,
    {
        info!("🔌 Starting enhanced WebSocket message listener...");

        // Start WebSocket connection
        let websocket_manager = self.websocket_manager.clone();
        let mut start_handle = tokio::spawn(async move {
            if let Err(e) = websocket_manager.start().await {
                error!("Failed to start WebSocket manager: {}", e);
            }
        });

        // Subscribe to messages
        let mut message_rx = self.subscribe_to_messages();

        // Message processing loop
        loop {
            tokio::select! {
                // Handle incoming messages
                message_result = message_rx.recv() => {
                    match message_result {
                        Ok(message) => {
                            debug!("📨 Received WebSocket message: {:?}", message);
                            message_handler(message).await;
                        }
                        Err(e) => {
                            error!("Message channel error: {}", e);
                            break;
                        }
                    }
                }

                // Check if WebSocket manager task completed
                _ = &mut start_handle => {
                    warn!("WebSocket manager task completed unexpectedly");
                    break;
                }
            }
        }

        // Stop WebSocket connection
        self.stop_websocket().await?;

        info!("✅ WebSocket message listener stopped");
        Ok(())
    }

    /// Legacy method - now handled by WebSocket manager
    async fn connect_and_listen<F, Fut>(&self, _message_handler: &mut F) -> BridgeResult<()>
    where
        F: FnMut(IncomingWhatsAppData) -> Fut + Send,
        Fut: std::future::Future<Output = BridgeResult<()>> + Send,
    {
        // This method is deprecated - WebSocket functionality is now handled by WebSocketManager
        Err(Box::new(BridgeError::ServiceUnavailable(
            "Legacy WebSocket method - use WebSocket manager instead".to_string(),
        )))
    }

    /// Send WhatsApp message via chat-port HTTP API
    #[instrument(skip(self, request), fields(to = request.number))]
    pub async fn send_message(
        &self,
        request: SendMessageRequest,
    ) -> BridgeResult<SendMessageResponse> {
        let url = format!("{}/send", self.config.api_base_url);

        debug!("Sending WhatsApp message to: {}", request.number);

        let response = self
            .http_client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&request)
            .send()
            .await
            .map_err(BridgeError::Http)?;

        let status = response.status();
        debug!("Chat-port send response status: {}", status);

        if status.is_success() {
            let send_response: SendMessageResponse =
                response.json().await.map_err(BridgeError::Http)?;

            if send_response.success {
                info!(
                    "WhatsApp message sent successfully - Message ID: {:?}",
                    send_response.message_id
                );
            } else {
                warn!("WhatsApp message send failed: {:?}", send_response.error);
            }

            Ok(send_response)
        } else {
            let error_text = response
                .text()
                .await
                .unwrap_or_else(|_| "Unknown error".to_string());

            error!(
                "Chat-port send error - Status: {}, Body: {}",
                status, error_text
            );

            Err(Box::new(BridgeError::ChatPort {
                message: error_text,
            }))
        }
    }

    /// Check if chat-port service is healthy
    #[instrument(skip(self))]
    pub async fn health_check(&self) -> BridgeResult<bool> {
        let url = format!("{}/health", self.config.api_base_url);

        debug!("Checking chat-port health at: {}", url);

        match self
            .http_client
            .get(&url)
            .timeout(Duration::from_secs(10))
            .send()
            .await
        {
            Ok(response) => {
                let is_healthy = response.status().is_success();
                debug!("Chat-port health check result: {}", is_healthy);
                Ok(is_healthy)
            }
            Err(e) => {
                warn!("Chat-port health check failed: {}", e);
                Ok(false)
            }
        }
    }
}
