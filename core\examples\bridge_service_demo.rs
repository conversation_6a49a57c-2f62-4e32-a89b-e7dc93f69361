/*!
# 🌉 Bridge Service Comprehensive Demo

A complete demonstration of the Wellbot Bridge Service showcasing the full message flow
from WhatsApp through JID authorization, AI processing, and response delivery.

## Features

- ✨ Beautiful interactive CLI with cliclack
- 🔐 JID authorization management
- 🤖 AI-powered message processing
- 🔌 Enhanced WebSocket communication
- 📊 Real-time monitoring and statistics
- 🎯 Complete message flow demonstration
- 🧪 Error handling and recovery

## Usage

```bash
cargo run --example bridge_service_demo --features examples
```

## Message Flow

```
WhatsApp Message → WebSocket → JID Check → AI Processing → Response → WhatsApp
```

This example demonstrates:
1. Setting up the bridge service with all components
2. Configuring JID authorization for specific WhatsApp accounts
3. Processing incoming messages with AI integration
4. Monitoring service health and statistics
5. Handling errors gracefully
*/

use std::{sync::Arc, time::Duration};

use cliclack::{
    confirm, input, intro, log, note, outro, select, spinner,
};
use console::style;
use tokio::time::sleep;
use wellbot_bridge::{
    config::Config,
    services::{
        bridge_service::{BridgeService, BridgeServiceConfig},
        jid_authorization::{JidAuthConfig, JidAuthorizationService},
    },
    types::{IncomingWhatsAppData, WebSocketMessage},
};

/// Demo operation modes
#[derive(Debug, Clone, Copy, Eq, PartialEq)]
enum DemoMode {
    FullDemo,
    ServiceSetup,
    MessageFlow,
    Monitoring,
    JidManagement,
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Set up Ctrl-C handler for graceful exit
    ctrlc::set_handler(move || {
        println!("\n🛑 Received interrupt signal - cleaning up...");
        std::process::exit(0);
    })
    .expect("Error setting Ctrl-C handler");

    // Clear screen and show beautiful intro
    cliclack::clear_screen()?;

    intro(style(" 🌉 Wellbot Bridge Service Demo ").on_blue().white())?;

    log::info("Welcome to the comprehensive Wellbot Bridge Service demonstration!")?;
    log::remark("This demo showcases the complete WhatsApp AI integration pipeline")?;

    // Show architecture overview
    note(
        "🏗️ Architecture Overview",
        "┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐\n│   WhatsApp      │───▶│  Bridge Service │───▶│   AI Service    │\n│   Messages      │    │                 │    │   (Genuis)      │\n│                 │◄───│ • JID Filter    │◄───│                 │\n│                 │    │ • WebSocket     │    │                 │\n│                 │    │ • Processing    │    │                 │\n└─────────────────┘    └─────────────────┘    └─────────────────┘",
    )?;

    // Demo mode selection
    let demo_mode = select("What would you like to demonstrate?")
        .initial_value(DemoMode::FullDemo)
        .item(
            DemoMode::FullDemo,
            "🎯 Complete Demo",
            "Full end-to-end demonstration of all features",
        )
        .item(
            DemoMode::ServiceSetup,
            "🚀 Service Setup",
            "Initialize and configure the bridge service",
        )
        .item(
            DemoMode::MessageFlow,
            "📨 Message Flow",
            "Demonstrate WhatsApp message processing",
        )
        .item(
            DemoMode::JidManagement,
            "🔐 JID Management",
            "Manage authorized WhatsApp accounts",
        )
        .item(
            DemoMode::Monitoring,
            "📊 Monitoring",
            "Real-time service monitoring and statistics",
        )
        .interact()?;

    match demo_mode {
        DemoMode::FullDemo => run_full_demo().await?,
        DemoMode::ServiceSetup => run_service_setup_demo().await?,
        DemoMode::MessageFlow => run_message_flow_demo().await?,
        DemoMode::JidManagement => run_jid_management_demo().await?,
        DemoMode::Monitoring => run_monitoring_demo().await?,
    }

    outro("🎉 Thank you for exploring the Wellbot Bridge Service!")?;
    Ok(())
}

/// Run the complete demonstration
async fn run_full_demo() -> Result<(), Box<dyn std::error::Error>> {
    log::info("🎯 Starting complete bridge service demonstration...")?;

    // Step 1: Service Setup
    log::step("Step 1: Service Setup")?;
    let (bridge_service, _jid_service) = setup_bridge_service().await?;

    // Step 2: JID Configuration
    log::step("Step 2: JID Authorization")?;
    configure_demo_jids(&bridge_service).await?;

    // Step 3: Start Services
    log::step("Step 3: Starting Services")?;
    start_services(&bridge_service).await?;

    // Step 4: Simulate Message Flow
    log::step("Step 4: Message Processing Demo")?;
    simulate_message_flow(&bridge_service).await?;

    // Step 5: Monitoring
    log::step("Step 5: Service Monitoring")?;
    monitor_service(&bridge_service).await?;

    // Step 6: Cleanup
    log::step("Step 6: Graceful Shutdown")?;
    cleanup_services(&bridge_service).await?;

    log::success("✅ Complete demonstration finished successfully!")?;
    Ok(())
}

/// Set up the bridge service with all components
async fn setup_bridge_service() -> Result<(Arc<BridgeService>, Arc<JidAuthorizationService>), Box<dyn std::error::Error>> {
    let spinner = spinner();
    spinner.start("🔧 Initializing bridge service components...");

    // Load configuration
    let config = Config::from_env()?;

    // Create JID authorization service
    let jid_config = JidAuthConfig::default();
    let jid_service = Arc::new(JidAuthorizationService::new(jid_config).await?);

    // Create bridge service configuration
    let bridge_config = BridgeServiceConfig {
        allowed_jids: vec![
            "<EMAIL>".to_string(),
            "<EMAIL>".to_string(),
        ],
        enable_background_processing: true,
        websocket_auto_reconnect: true,
        ai_processing_enabled: true,
        max_concurrent_ai_requests: 5,
    };

    // Initialize bridge service
    let bridge_service = Arc::new(
        BridgeService::new_with_runtime_config(config, bridge_config).await?
    );

    spinner.stop("✅ Bridge service components initialized");

    log::info("🎉 Bridge service ready with:")?;
    log::remark("  • AI client integration")?;
    log::remark("  • WebSocket manager")?;
    log::remark("  • JID authorization")?;
    log::remark("  • Message processor")?;

    Ok((bridge_service, jid_service))
}

/// Configure demo JIDs
async fn configure_demo_jids(bridge_service: &BridgeService) -> Result<(), Box<dyn std::error::Error>> {
    let spinner = spinner();
    spinner.start("🔐 Configuring demo JIDs...");

    // Add demo JIDs
    bridge_service.add_allowed_jid("<EMAIL>".to_string()).await?;
    bridge_service.add_allowed_jid("<EMAIL>".to_string()).await?;

    let jids = bridge_service.get_allowed_jids().await;
    spinner.stop(&format!("✅ Configured {} authorized JIDs", jids.len()));

    for jid in jids {
        log::remark(&format!("  • {}", jid))?;
    }

    Ok(())
}

/// Start all services
async fn start_services(bridge_service: &BridgeService) -> Result<(), Box<dyn std::error::Error>> {
    let spinner = spinner();
    spinner.start("🚀 Starting bridge service...");

    bridge_service.start().await?;

    spinner.stop("✅ Bridge service started successfully");

    // Check health
    let healthy = bridge_service.health_check().await?;
    if healthy {
        log::success("🟢 All services are healthy and ready")?;
    } else {
        log::warning("🟡 Some services may not be fully available")?;
    }

    Ok(())
}

/// Simulate message flow
async fn simulate_message_flow(bridge_service: &BridgeService) -> Result<(), Box<dyn std::error::Error>> {
    log::info("📨 Simulating WhatsApp message processing...")?;

    // Create demo messages
    let demo_messages = vec![
        IncomingWhatsAppData {
            from: "<EMAIL>".to_string(),
            message: "Hello! Can you help me with medication information?".to_string(),
            message_id: "demo_msg_1".to_string(),
            timestamp: chrono::Utc::now(),
        },
        IncomingWhatsAppData {
            from: "<EMAIL>".to_string(),
            message: "What are the side effects of aspirin?".to_string(),
            message_id: "demo_msg_2".to_string(),
            timestamp: chrono::Utc::now(),
        },
        IncomingWhatsAppData {
            from: "<EMAIL>".to_string(),
            message: "This should be blocked".to_string(),
            message_id: "demo_msg_3".to_string(),
            timestamp: chrono::Utc::now(),
        },
    ];

    for (i, message) in demo_messages.iter().enumerate() {
        let spinner = spinner();
        spinner.start(&format!("📤 Processing message {} from {}", i + 1, message.from));

        match bridge_service.process_whatsapp_message(message.clone()).await {
            Ok(_) => {
                spinner.stop(&format!("✅ Message {} processed successfully", i + 1));
            }
            Err(e) => {
                spinner.stop(&format!("❌ Message {} failed: {}", i + 1, e));
            }
        }

        sleep(Duration::from_millis(500)).await;
    }

    Ok(())
}

/// Monitor service statistics
async fn monitor_service(bridge_service: &BridgeService) -> Result<(), Box<dyn std::error::Error>> {
    log::info("📊 Displaying service statistics...")?;

    let stats = bridge_service.get_stats().await;

    log::info("📈 Bridge Service Statistics:")?;
    log::remark(&format!("  Messages Processed: {}", stats.messages_processed))?;
    log::remark(&format!("  Authorized Messages: {}", stats.messages_authorized))?;
    log::remark(&format!("  Unauthorized Messages: {}", stats.messages_unauthorized))?;
    log::remark(&format!("  AI Requests: {}", stats.ai_requests_sent))?;
    log::remark(&format!("  AI Responses: {}", stats.ai_responses_received))?;
    log::remark(&format!("  Errors: {}", stats.errors_encountered))?;

    Ok(())
}

/// Cleanup services
async fn cleanup_services(bridge_service: &BridgeService) -> Result<(), Box<dyn std::error::Error>> {
    let spinner = spinner();
    spinner.start("🛑 Shutting down services...");

    bridge_service.stop().await?;

    spinner.stop("✅ Services shut down gracefully");
    Ok(())
}

/// Run service setup demo
async fn run_service_setup_demo() -> Result<(), Box<dyn std::error::Error>> {
    log::info("🚀 Service setup demonstration...")?;
    let (bridge_service, _) = setup_bridge_service().await?;
    start_services(&bridge_service).await?;
    cleanup_services(&bridge_service).await?;
    Ok(())
}

/// Run message flow demo
async fn run_message_flow_demo() -> Result<(), Box<dyn std::error::Error>> {
    log::info("📨 Message flow demonstration...")?;
    let (bridge_service, _) = setup_bridge_service().await?;
    configure_demo_jids(&bridge_service).await?;
    start_services(&bridge_service).await?;
    simulate_message_flow(&bridge_service).await?;
    cleanup_services(&bridge_service).await?;
    Ok(())
}

/// Run JID management demo
async fn run_jid_management_demo() -> Result<(), Box<dyn std::error::Error>> {
    log::info("🔐 JID management demonstration...")?;
    let (bridge_service, _) = setup_bridge_service().await?;
    configure_demo_jids(&bridge_service).await?;
    
    // Show current JIDs
    let jids = bridge_service.get_allowed_jids().await;
    log::info(&format!("Current authorized JIDs: {}", jids.len()))?;
    
    cleanup_services(&bridge_service).await?;
    Ok(())
}

/// Run monitoring demo
async fn run_monitoring_demo() -> Result<(), Box<dyn std::error::Error>> {
    log::info("📊 Monitoring demonstration...")?;
    let (bridge_service, _) = setup_bridge_service().await?;
    start_services(&bridge_service).await?;
    monitor_service(&bridge_service).await?;
    cleanup_services(&bridge_service).await?;
    Ok(())
}
