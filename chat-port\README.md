# ChatPort - Professional WhatsApp Service

[![Go Version](https://img.shields.io/badge/Go-1.24.2+-blue.svg)](https://golang.org)
[![Test Coverage](https://img.shields.io/badge/Coverage-80%25+-green.svg)](#testing)
[![License](https://img.shields.io/badge/License-Proprietary-red.svg)](#license)

ChatPort is a production-ready, enterprise-grade Go service that provides comprehensive WhatsApp messaging capabilities for the Wellbot system. It serves as a pure WhatsApp gateway, handling message sending, receiving, and WebSocket broadcasting, featuring real-time communication, robust monitoring, and professional-grade reliability.

## 🏗️ System Architecture

ChatPort operates as a pure WhatsApp gateway service:

```
┌─────────────────┐    ┌─────────────────┐
│   ChatPort      │    │   Wellbot       │
│   (Go Service)  │◄──►│  (Rust Bridge)  │
│                 │    │                 │
│ • WhatsApp API  │    │ • AI Processing │
│ • WebSocket     │    │ • Message Logic │
│ • REST API      │    │ • Intelligence  │
│ • Monitoring    │    │ • Filtering     │
└─────────────────┘    └─────────────────┘
```

## ✨ Key Features

### 🔌 **Real-time Communication**

- **WebSocket Server**: Live bidirectional communication with clients
- **Message Broadcasting**: Real-time WhatsApp message streaming
- **Status Updates**: Live service health and connection status
- **Metrics Streaming**: Real-time performance monitoring

### 📱 **WhatsApp Integration**

- **Business API Support**: Full WhatsApp Business API via whatsmeow
- **QR Code Authentication**: Seamless setup with mobile scanning
- **Multi-device Support**: Concurrent device management
- **Message Types**: Text, media, and rich message support

### 🛡️ **Enterprise-Grade Reliability**

- **Health Monitoring**: Comprehensive health checks and metrics
- **Graceful Shutdown**: Proper cleanup and connection management
- **Error Recovery**: Automatic reconnection and retry mechanisms
- **Circuit Breakers**: Fault tolerance and service protection

### 📊 **Observability & Monitoring**

- **Structured Logging**: JSON/text formats with configurable levels
- **Metrics Collection**: Performance, success rates, and usage statistics
- **Health Endpoints**: Service status and component health
- **Real-time Dashboards**: WebSocket-powered monitoring interfaces

### ⚙️ **Production Features**

- **Configuration Management**: Environment-based configuration
- **Security**: Rate limiting, CORS, and authentication support
- **Scalability**: Concurrent connection handling (1000+ WebSocket clients)
- **Testing**: Comprehensive test suite with 80%+ coverage

## 📋 Prerequisites

- Go 1.24.2 or later
- SQLite3 (for session storage)
- Access to WhatsApp Business API
- Running Med-Intel service (Rust AI service)

## 🛠️ Installation

1. **Clone the repository**:

   ```bash
   git clone <repository-url>
   cd chat-port
   ```

2. **Install dependencies**:

   ```bash
   go mod download
   ```

3. **Configure environment** (copy and edit):

   ```bash
   cp .env.example .env
   ```

4. **Build the application**:
   ```bash
   go build -o chatport ./cmd
   ```

## ⚙️ Configuration

Configuration is managed through environment variables for maximum flexibility and security. See `.env.example` for all available options:

### 🌐 Server Configuration

```bash
SERVER_PORT=8081                    # HTTP server port
SERVER_READ_TIMEOUT=15s             # Request read timeout
SERVER_WRITE_TIMEOUT=15s            # Response write timeout
SERVER_IDLE_TIMEOUT=60s             # Connection idle timeout
```

### 📱 WhatsApp Configuration

```bash
WHATSAPP_DB_PATH=file:session.db?_foreign_keys=on  # Session database path
WHATSAPP_QR_TIMEOUT=2m              # QR code scan timeout
WHATSAPP_RECONNECT_DELAY=5s         # Reconnection delay
WHATSAPP_MAX_RECONNECTS=5           # Maximum reconnection attempts
```

### 🔌 WebSocket Configuration

```bash
WEBSOCKET_ENABLED=true              # Enable WebSocket functionality
WEBSOCKET_MAX_CLIENTS=1000          # Maximum concurrent connections
WEBSOCKET_CLIENT_TIMEOUT=5m         # Client connection timeout
WEBSOCKET_CLEANUP_INTERVAL=30s      # Inactive client cleanup interval
WEBSOCKET_READ_BUFFER_SIZE=1024     # Read buffer size (bytes)
WEBSOCKET_WRITE_BUFFER_SIZE=1024    # Write buffer size (bytes)
WEBSOCKET_BROADCAST_BUFFER_SIZE=1000 # Broadcast message buffer
WEBSOCKET_ENABLE_COMPRESSION=true   # Enable message compression
WEBSOCKET_METRICS_INTERVAL=10s      # Metrics broadcast interval
WEBSOCKET_ENABLE_AUTH=false         # Enable WebSocket authentication
WEBSOCKET_RATE_LIMIT=10             # Connections per time window
WEBSOCKET_RATE_LIMIT_WINDOW=1m      # Rate limit time window
```

### 📝 Logging Configuration

```bash
LOG_LEVEL=info                      # Logging level: debug, info, warn, error
LOG_FORMAT=text                     # Log format: text, json
```

## 🚀 Usage

### Starting the Service

```bash
# Using the binary
./chatport

# Or using go run
go run ./cmd
```

### First Time Setup

1. Start the service
2. Scan the QR code displayed in the console with WhatsApp
3. The service will automatically connect and start processing messages

### 🌐 API Endpoints

#### REST API

**Send WhatsApp Message**

```http
POST /api/send
Content-Type: application/json

{
  "number": "**********",
  "message": "Hello from ChatPort!"
}
```

**Health Check**

```http
GET /api/health
```

Returns service health status and component availability.

**Service Status**

```http
GET /api/status
```

Returns detailed service information including uptime, system metrics, and WhatsApp connection status.

**Performance Metrics**

```http
GET /api/metrics
```

Returns comprehensive performance metrics including message statistics and success rates.

**WebSocket Statistics**

```http
GET /api/ws/stats
```

Returns WebSocket hub statistics, active connections, and configuration details.

#### WebSocket API

**Real-time Connection**

```javascript
// Connect to WebSocket endpoint
const ws = new WebSocket("ws://localhost:8081/ws");

// Subscribe to WhatsApp messages
ws.send(
  JSON.stringify({
    type: "subscribe",
    data: { action: "subscribe", subscription: "whatsapp" },
  })
);
```

**Available Subscriptions:**

- `whatsapp` - Incoming WhatsApp messages
- `status` - Service status updates
- `metrics` - Real-time performance metrics
- `health` - Health status changes
- `all` - All message types

See [WebSocket Documentation](docs/WEBSOCKET.md) for complete API reference.

## 📊 Monitoring & Observability

ChatPort provides enterprise-grade monitoring and observability features:

### 🔍 Health Monitoring

- **Service Health**: Real-time service availability and component status
- **WhatsApp Connection**: Live connection status and authentication state
- **System Resources**: Memory usage, goroutine count, and system metrics

### 📈 Performance Metrics

- **Message Statistics**: Received, sent, failed message counts with success rates
- **HTTP Metrics**: Request counts, response times, and error tracking
- **WebSocket Metrics**: Active connections, message throughput, and client statistics
- **System Performance**: Uptime, resource utilization, and throughput metrics

### 🔌 Real-time Monitoring

- **WebSocket Dashboard**: Live connection monitoring and message streaming
- **Status Broadcasting**: Real-time service status updates
- **Metrics Streaming**: Live performance data with configurable intervals
- **Health Alerts**: Immediate notification of status changes

### 📝 Structured Logging

- **Configurable Levels**: Debug, info, warn, error with runtime adjustment
- **Multiple Formats**: JSON for production, text for development
- **Request Tracing**: Complete request/response lifecycle logging
- **Error Tracking**: Detailed error logs with stack traces and context
- **Performance Logging**: Response times and resource usage tracking

### 🎯 Monitoring Endpoints

```bash
# Health status with component details
curl http://localhost:8081/api/health

# Comprehensive service status
curl http://localhost:8081/api/status

# Performance metrics and statistics
curl http://localhost:8081/api/metrics

# WebSocket hub statistics
curl http://localhost:8081/api/ws/stats
```

## 🧪 Testing

ChatPort includes a comprehensive test suite with 80%+ coverage across all packages:

### Running Tests

```bash
# Run all tests with coverage
go test -cover ./...

# Run tests with verbose output
go test -v ./...

# Run specific package tests
go test ./internal/handlers
go test ./internal/websocket
go test ./config

# Run tests with race detection
go test -race ./...

# Generate coverage report
go test -coverprofile=coverage.out ./...
go tool cover -html=coverage.out
```

### Test Coverage by Package

| Package               | Coverage | Status       |
| --------------------- | -------- | ------------ |
| `config`              | 77.6%    | ✅ Good      |
| `internal/errors`     | 95.6%    | ✅ Excellent |
| `internal/handlers`   | 73.8%    | ✅ Good      |
| `internal/logger`     | 84.1%    | ✅ Excellent |
| `internal/monitoring` | 100.0%   | ✅ Perfect   |
| `internal/server`     | 89.5%    | ✅ Excellent |
| `internal/websocket`  | 77.0%    | ✅ Good      |

### Test Categories

- **Unit Tests**: Individual component testing with mocks
- **Integration Tests**: End-to-end API testing
- **WebSocket Tests**: Real-time communication testing
- **Concurrent Tests**: Race condition and thread safety testing
- **Error Handling Tests**: Comprehensive error scenario coverage

## 🏗️ Project Structure

```
chat-port/
├── cmd/                    # Application entry point
│   └── main.go
├── config/                 # Configuration management
│   ├── config.go
│   └── config_test.go
├── internal/               # Internal packages
│   ├── client/            # WhatsApp client integration
│   │   ├── client.go
│   │   └── client_test.go
│   ├── errors/            # Error handling utilities
│   │   ├── errors.go
│   │   └── errors_test.go
│   ├── handlers/          # HTTP REST API handlers
│   │   ├── health.go
│   │   ├── metrics.go
│   │   ├── send.go
│   │   ├── status.go
│   │   └── *_test.go
│   ├── logger/            # Structured logging
│   │   ├── logger.go
│   │   └── logger_test.go
│   ├── monitoring/        # Metrics and monitoring
│   │   ├── metrics.go
│   │   └── metrics_test.go
│   ├── server/            # HTTP server and routing
│   │   ├── server.go
│   │   └── server_test.go
│   └── websocket/         # Real-time WebSocket communication
│       ├── types.go       # Message types and protocol
│       ├── client.go      # WebSocket client management
│       ├── hub.go         # Connection hub and broadcasting
│       ├── handler.go     # HTTP upgrade and middleware
│       ├── broadcaster.go # Message broadcasting logic
│       └── *_test.go      # Comprehensive test suite
├── docs/                   # Documentation
│   └── WEBSOCKET.md       # WebSocket API documentation
├── examples/               # Usage examples
│   └── websocket_client.html # HTML WebSocket client
├── .env.example           # Environment configuration template
├── go.mod                 # Go module definition
├── go.sum                 # Go module checksums
└── README.md              # This comprehensive guide
```

## 🔧 Development

### 🚀 Quick Start for Developers

```bash
# Clone and setup
git clone <repository-url>
cd chat-port
go mod download

# Run tests
go test -cover ./...

# Start development server
go run ./cmd

# Build for production
go build -o chatport ./cmd
```

### 📋 Development Workflow

1. **Create Feature Branch**

   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Implement with Tests**

   - Write tests first (TDD approach)
   - Ensure 80%+ test coverage
   - Test both happy path and error scenarios

3. **Code Quality Checks**

   ```bash
   # Format code
   go fmt ./...

   # Run linter
   golangci-lint run

   # Check for race conditions
   go test -race ./...
   ```

4. **Update Documentation**

   - Update README.md for new features
   - Add/update API documentation
   - Include usage examples

5. **Submit Pull Request**
   - Ensure all tests pass
   - Include comprehensive description
   - Request code review

### 📏 Code Standards

- **Go Best Practices**: Follow effective Go guidelines and idioms
- **Error Handling**: Comprehensive error handling with proper context
- **Testing**: Write unit, integration, and benchmark tests
- **Documentation**: Document all public APIs and complex logic
- **Logging**: Use structured logging with appropriate levels
- **Security**: Validate inputs, handle secrets securely
- **Performance**: Consider memory allocation and goroutine management

### 🏗️ Architecture Guidelines

- **Clean Architecture**: Separate concerns with clear boundaries
- **Dependency Injection**: Use interfaces for testability
- **Configuration**: Environment-based configuration management
- **Monitoring**: Include metrics and health checks for new features
- **WebSocket Integration**: Follow established patterns for real-time features

## � Deployment

### 🐳 Docker Deployment

```dockerfile
# Dockerfile
FROM golang:1.24.2-alpine AS builder
WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download
COPY . .
RUN go build -o chatport ./cmd

FROM alpine:latest
RUN apk --no-cache add ca-certificates
WORKDIR /root/
COPY --from=builder /app/chatport .
EXPOSE 8081
CMD ["./chatport"]
```

```bash
# Build and run
docker build -t chatport .
docker run -p 8081:8081 --env-file .env chatport
```

### ☁️ Production Deployment

**Environment Setup**

```bash
# Production environment variables
export LOG_LEVEL=info
export LOG_FORMAT=json
export WEBSOCKET_ENABLED=true
export WEBSOCKET_MAX_CLIENTS=5000

```

**Systemd Service**

```ini
[Unit]
Description=ChatPort WhatsApp Service
After=network.target

[Service]
Type=simple
User=chatport
WorkingDirectory=/opt/chatport
ExecStart=/opt/chatport/chatport
Restart=always
RestartSec=5
EnvironmentFile=/opt/chatport/.env

[Install]
WantedBy=multi-user.target
```

**Load Balancer Configuration**

```nginx
upstream chatport {
    server 127.0.0.1:8081;
    server 127.0.0.1:8082;
}

server {
    listen 80;
    server_name chatport.yourdomain.com;

    location / {
        proxy_pass http://chatport;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /ws {
        proxy_pass http://chatport;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

## �🚨 Troubleshooting

### 🔍 Common Issues

**🔐 WhatsApp Authentication Issues**

```bash
# Check QR code display
export LOG_LEVEL=debug
./chatport

# Verify session database
ls -la session.db

# Clear session and re-authenticate
rm session.db
./chatport
```

**🔌 WebSocket Connection Issues**

```bash
# Test WebSocket connectivity
curl -i -N -H "Connection: Upgrade" \
     -H "Upgrade: websocket" \
     -H "Sec-WebSocket-Version: 13" \
     -H "Sec-WebSocket-Key: test" \
     http://localhost:8081/ws

# Check WebSocket statistics
curl http://localhost:8081/api/ws/stats
```

**📱 Message Delivery Issues**

```bash
# Test message sending
curl -X POST http://localhost:8081/api/send \
     -H "Content-Type: application/json" \
     -d '{"number":"**********","message":"test"}'

# Check WhatsApp connection status
curl http://localhost:8081/api/status | jq '.whatsapp'
```

### 🔧 Debugging Tools

**Enable Debug Logging**

```bash
export LOG_LEVEL=debug
export LOG_FORMAT=json
./chatport
```

**Health Check Commands**

```bash
# Overall service health
curl http://localhost:8081/api/health

# Detailed service status
curl http://localhost:8081/api/status

# Performance metrics
curl http://localhost:8081/api/metrics

# WebSocket hub status
curl http://localhost:8081/api/ws/stats
```

**Log Analysis**

```bash
# Filter error logs
./chatport 2>&1 | grep -i error

# Monitor WebSocket connections
./chatport 2>&1 | grep -i websocket

# Track message flow
./chatport 2>&1 | grep -i "message\|whatsapp"
```

## � Documentation

- **[WebSocket API Reference](docs/WEBSOCKET.md)** - Complete WebSocket documentation
- **[HTML Client Example](examples/websocket_client.html)** - Interactive WebSocket client
- **[Configuration Guide](#️-configuration)** - Environment variable reference
- **[API Documentation](#-api-endpoints)** - REST and WebSocket API reference

## 🤝 Contributing

We welcome contributions to ChatPort! Please follow these guidelines:

### 📋 Contribution Process

1. **Fork the Repository**

   ```bash
   git clone https://github.com/your-username/chatport.git
   cd chatport
   ```

2. **Create Feature Branch**

   ```bash
   git checkout -b feature/amazing-feature
   ```

3. **Make Your Changes**

   - Follow the [code standards](#-code-standards)
   - Add comprehensive tests
   - Update documentation

4. **Test Your Changes**

   ```bash
   go test -cover ./...
   go test -race ./...
   ```

5. **Submit Pull Request**
   - Provide clear description
   - Include test coverage
   - Reference related issues

### 🏆 Recognition

Contributors will be recognized in our [CONTRIBUTORS.md](CONTRIBUTORS.md) file.

## 📄 License

**Proprietary Software** - This project is part of the Wellbot pharmacy assistant system.

All rights reserved. This software and associated documentation files (the "Software") are proprietary and confidential. Unauthorized copying, distribution, or modification is strictly prohibited.

## 📞 Support & Contact

### 🆘 Getting Help

- **Documentation**: Start with this README and [WebSocket docs](docs/WEBSOCKET.md)
- **Issues**: Check existing [GitHub Issues](https://github.com/your-org/chatport/issues)
- **Discussions**: Join our [GitHub Discussions](https://github.com/your-org/chatport/discussions)

### 🐛 Bug Reports

When reporting bugs, please include:

- Go version and OS
- Configuration (sanitized)
- Steps to reproduce
- Expected vs actual behavior
- Relevant logs

### 💡 Feature Requests

We welcome feature requests! Please provide:

- Use case description
- Proposed implementation approach
- Potential impact assessment

---

<div align="center">

**ChatPort** - Professional WhatsApp Service for Wellbot

[![Go](https://img.shields.io/badge/Go-1.24.2+-blue.svg)](https://golang.org)
[![WebSocket](https://img.shields.io/badge/WebSocket-Enabled-green.svg)](#websocket-api)
[![Coverage](https://img.shields.io/badge/Coverage-80%25+-green.svg)](#testing)

_Built with ❤️ for the Wellbot ecosystem_

</div>
