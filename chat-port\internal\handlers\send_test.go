package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"sync"
	"testing"

	"chatport-go/internal/client"
)

// MockWhatsAppClient is a mock implementation for testing
type MockWhatsAppClient struct {
	connected    bool
	sendError    error
	messagesSent []MockMessage
}

type MockMessage struct {
	Number  string
	Message string
}

func (m *MockWhatsAppClient) IsConnected() bool {
	return m.connected
}

func (m *MockWhatsAppClient) SendMessage(number, message string) error {
	if m.sendError != nil {
		return m.sendError
	}
	m.messagesSent = append(m.messagesSent, MockMessage{
		Number:  number,
		Message: message,
	})
	return nil
}

func TestSendHandler(t *testing.T) {
	tests := []struct {
		name           string
		requestBody    interface{}
		clientSetup    func()
		expectedStatus int
		expectedError  string
	}{
		{
			name:           "invalid JSON payload",
			requestBody:    "invalid json",
			clientSetup:    func() { /* no setup needed */ },
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Invalid JSON payload",
		},
		{
			name: "missing number field",
			requestBody: SendRequest{
				Message: "Hello, World!",
			},
			clientSetup:    func() { /* no setup needed */ },
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Number and message are required",
		},
		{
			name: "missing message field",
			requestBody: SendRequest{
				Number: "**********",
			},
			clientSetup:    func() { /* no setup needed */ },
			expectedStatus: http.StatusBadRequest,
			expectedError:  "Number and message are required",
		},
		{
			name: "client not initialized",
			requestBody: SendRequest{
				Number:  "**********",
				Message: "Hello, World!",
			},
			clientSetup: func() {
				client.Client = nil
			},
			expectedStatus: http.StatusServiceUnavailable,
			expectedError:  "WhatsApp client not initialized",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup client state
			tt.clientSetup()

			// Create request body
			var body []byte
			var err error
			if str, ok := tt.requestBody.(string); ok {
				body = []byte(str)
			} else {
				body, err = json.Marshal(tt.requestBody)
				if err != nil {
					t.Fatalf("Failed to marshal request body: %v", err)
				}
			}

			// Create request
			req := httptest.NewRequest(http.MethodPost, "/api/send", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			// Create response recorder
			rr := httptest.NewRecorder()

			// Call handler
			SendHandler(rr, req)

			// Check status code
			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			// Check error message if expected
			if tt.expectedError != "" {
				var response SendResponse
				err := json.NewDecoder(rr.Body).Decode(&response)
				if err != nil {
					t.Fatalf("Failed to decode response: %v", err)
				}

				if response.Error != tt.expectedError {
					t.Errorf("Expected error %q, got %q", tt.expectedError, response.Error)
				}

				if response.Success {
					t.Error("Expected success to be false for error cases")
				}
			}

			// Check success response
			if tt.expectedStatus == http.StatusOK {
				var response SendResponse
				err := json.NewDecoder(rr.Body).Decode(&response)
				if err != nil {
					t.Fatalf("Failed to decode response: %v", err)
				}

				if !response.Success {
					t.Error("Expected success to be true")
				}

				if response.Message == "" {
					t.Error("Expected success message to be present")
				}
			}
		})
	}
}

func TestRespondWithJSON(t *testing.T) {
	tests := []struct {
		name           string
		payload        interface{}
		expectedStatus int
	}{
		{
			name: "valid payload",
			payload: SendResponse{
				Success: true,
				Message: "Test message",
			},
			expectedStatus: http.StatusOK,
		},
		{
			name:           "nil payload",
			payload:        nil,
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rr := httptest.NewRecorder()

			respondWithJSON(rr, tt.expectedStatus, tt.payload)

			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			contentType := rr.Header().Get("Content-Type")
			if contentType != "application/json" {
				t.Errorf("Expected Content-Type application/json, got %s", contentType)
			}

			// Verify JSON is valid
			var result interface{}
			err := json.NewDecoder(rr.Body).Decode(&result)
			if err != nil {
				t.Errorf("Response is not valid JSON: %v", err)
			}
		})
	}
}

func TestRespondWithError(t *testing.T) {
	tests := []struct {
		name           string
		code           int
		message        string
		expectedStatus int
	}{
		{
			name:           "bad request error",
			code:           http.StatusBadRequest,
			message:        "Invalid input",
			expectedStatus: http.StatusBadRequest,
		},
		{
			name:           "internal server error",
			code:           http.StatusInternalServerError,
			message:        "Something went wrong",
			expectedStatus: http.StatusInternalServerError,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			rr := httptest.NewRecorder()

			respondWithError(rr, tt.code, tt.message)

			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			var response SendResponse
			err := json.NewDecoder(rr.Body).Decode(&response)
			if err != nil {
				t.Fatalf("Failed to decode response: %v", err)
			}

			if response.Success {
				t.Error("Expected success to be false")
			}

			if response.Error != tt.message {
				t.Errorf("Expected error message %q, got %q", tt.message, response.Error)
			}
		})
	}
}

func TestSendRequest_JSONMarshaling(t *testing.T) {
	request := SendRequest{
		Number:  "**********",
		Message: "Test message",
	}

	// Test marshaling
	data, err := json.Marshal(request)
	if err != nil {
		t.Fatalf("Failed to marshal SendRequest: %v", err)
	}

	// Test unmarshaling
	var unmarshaled SendRequest
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal SendRequest: %v", err)
	}

	if unmarshaled.Number != request.Number {
		t.Errorf("Expected number %s, got %s", request.Number, unmarshaled.Number)
	}

	if unmarshaled.Message != request.Message {
		t.Errorf("Expected message %s, got %s", request.Message, unmarshaled.Message)
	}
}

func TestSendResponse_JSONMarshaling(t *testing.T) {
	response := SendResponse{
		Success: true,
		Message: "Success message",
		Error:   "Error message",
	}

	// Test marshaling
	data, err := json.Marshal(response)
	if err != nil {
		t.Fatalf("Failed to marshal SendResponse: %v", err)
	}

	// Test unmarshaling
	var unmarshaled SendResponse
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal SendResponse: %v", err)
	}

	if unmarshaled.Success != response.Success {
		t.Errorf("Expected success %v, got %v", response.Success, unmarshaled.Success)
	}

	if unmarshaled.Message != response.Message {
		t.Errorf("Expected message %s, got %s", response.Message, unmarshaled.Message)
	}

	if unmarshaled.Error != response.Error {
		t.Errorf("Expected error %s, got %s", response.Error, unmarshaled.Error)
	}
}

func TestSendHandler_EmptyBody(t *testing.T) {
	req := httptest.NewRequest(http.MethodPost, "/api/send", nil)
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()
	SendHandler(rr, req)

	if rr.Code != http.StatusBadRequest {
		t.Errorf("Expected status 400 for empty body, got %d", rr.Code)
	}

	var response SendResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	if response.Success {
		t.Error("Expected success to be false")
	}

	if response.Error == "" {
		t.Error("Expected error message to be present")
	}
}

func TestSendHandler_ContentType(t *testing.T) {
	// Test with wrong content type
	payload := SendRequest{
		Number:  "**********",
		Message: "Hello, World!",
	}

	body, _ := json.Marshal(payload)
	req := httptest.NewRequest(http.MethodPost, "/api/send", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "text/plain") // Wrong content type

	rr := httptest.NewRecorder()
	SendHandler(rr, req)

	// Should still work as Go's JSON decoder is flexible
	// But in a real scenario, you might want to validate content type
	if rr.Code == http.StatusUnsupportedMediaType {
		t.Log("Handler correctly rejects wrong content type")
	}
}

func TestSendHandler_LargePayload(t *testing.T) {
	// Test with very large message
	largeMessage := strings.Repeat("A", 10000) // 10KB message

	payload := SendRequest{
		Number:  "**********",
		Message: largeMessage,
	}

	body, _ := json.Marshal(payload)
	req := httptest.NewRequest(http.MethodPost, "/api/send", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()
	SendHandler(rr, req)

	// Should handle large payloads gracefully
	// The actual behavior depends on your business requirements
	// 503 is expected when client is not connected
	if rr.Code >= 500 && rr.Code != http.StatusServiceUnavailable {
		t.Errorf("Handler should not fail with 5xx error except 503 for large payload, got %d", rr.Code)
	}
}

func TestSendHandler_SpecialCharacters(t *testing.T) {
	// Test with special characters and emojis
	payload := SendRequest{
		Number:  "**********",
		Message: "Hello 🌍! Special chars: àáâãäåæçèéêë",
	}

	body, _ := json.Marshal(payload)
	req := httptest.NewRequest(http.MethodPost, "/api/send", bytes.NewBuffer(body))
	req.Header.Set("Content-Type", "application/json")

	rr := httptest.NewRecorder()
	SendHandler(rr, req)

	// Should handle Unicode characters properly
	// 503 is expected when client is not connected
	if rr.Code >= 500 && rr.Code != http.StatusServiceUnavailable {
		t.Errorf("Handler should not fail with 5xx error except 503 for Unicode characters, got %d", rr.Code)
	}
}

func TestSendHandler_NumberFormats(t *testing.T) {
	tests := []struct {
		name   string
		number string
		valid  bool
	}{
		{"standard format", "**********", true},
		{"with country code", "+**********", true},
		{"with spaces", "123 456 7890", true},
		{"with dashes", "123-456-7890", true},
		{"empty number", "", false},
		{"only spaces", "   ", false},
		{"letters", "abcdefghij", true}, // Might be valid depending on business rules
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			payload := SendRequest{
				Number:  tt.number,
				Message: "Test message",
			}

			body, _ := json.Marshal(payload)
			req := httptest.NewRequest(http.MethodPost, "/api/send", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			rr := httptest.NewRecorder()
			SendHandler(rr, req)

			if !tt.valid && rr.Code == http.StatusOK {
				t.Errorf("Expected error for invalid number format %q", tt.number)
			}
		})
	}
}

func TestRespondWithJSON_ErrorHandling(t *testing.T) {
	// Test with a payload that can't be marshaled
	type UnmarshalableType struct {
		Channel chan int `json:"channel"` // Channels can't be marshaled to JSON
	}

	payload := UnmarshalableType{
		Channel: make(chan int),
	}

	rr := httptest.NewRecorder()
	respondWithJSON(rr, http.StatusOK, payload)

	// Should return 500 error when JSON marshaling fails
	if rr.Code != http.StatusInternalServerError {
		t.Errorf("Expected status 500 for unmarshalable payload, got %d", rr.Code)
	}
}

func TestSendHandler_ConcurrentRequests(t *testing.T) {
	// Test that handler can handle concurrent requests
	const numRequests = 10

	var wg sync.WaitGroup
	results := make([]int, numRequests)

	for i := 0; i < numRequests; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			payload := SendRequest{
				Number:  fmt.Sprintf("123456789%d", index),
				Message: fmt.Sprintf("Test message %d", index),
			}

			body, _ := json.Marshal(payload)
			req := httptest.NewRequest(http.MethodPost, "/api/send", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			rr := httptest.NewRecorder()
			SendHandler(rr, req)

			results[index] = rr.Code
		}(i)
	}

	wg.Wait()

	// Check that all requests were handled
	for i, code := range results {
		if code == 0 {
			t.Errorf("Request %d was not handled", i)
		}
	}
}

func TestSendHandler_WhitespaceValidation(t *testing.T) {
	tests := []struct {
		name    string
		number  string
		message string
		valid   bool
	}{
		{
			name:    "whitespace only number",
			number:  "   ",
			message: "Hello",
			valid:   false,
		},
		{
			name:    "whitespace only message",
			number:  "**********",
			message: "   ",
			valid:   false,
		},
		{
			name:    "both whitespace only",
			number:  "   ",
			message: "   ",
			valid:   false,
		},
		{
			name:    "valid with leading/trailing spaces",
			number:  " ********** ",
			message: " Hello World ",
			valid:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			payload := SendRequest{
				Number:  tt.number,
				Message: tt.message,
			}

			body, _ := json.Marshal(payload)
			req := httptest.NewRequest(http.MethodPost, "/api/send", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			rr := httptest.NewRecorder()
			SendHandler(rr, req)

			if !tt.valid && rr.Code == http.StatusOK {
				t.Errorf("Expected error for invalid input: number=%q, message=%q", tt.number, tt.message)
			}
		})
	}
}

func TestSendHandler_HTTPMethods(t *testing.T) {
	payload := SendRequest{
		Number:  "**********",
		Message: "Test message",
	}

	body, _ := json.Marshal(payload)

	methods := []string{"GET", "PUT", "DELETE", "PATCH"}

	for _, method := range methods {
		t.Run(method, func(t *testing.T) {
			req := httptest.NewRequest(method, "/api/send", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")

			rr := httptest.NewRecorder()
			SendHandler(rr, req)

			// Handler should work with any method (though in practice it should be POST only)
			// The routing layer would typically handle method restrictions
			// 503 is expected when client is not connected
			if rr.Code >= 500 && rr.Code != http.StatusServiceUnavailable {
				t.Errorf("Handler should not fail with 5xx error except 503 for %s method, got %d", method, rr.Code)
			}
		})
	}
}

func TestSendHandler_MalformedJSON(t *testing.T) {
	malformedJSONs := []string{
		`{"number": "123", "message":}`,         // Missing value
		`{"number": "123" "message": "test"}`,   // Missing comma
		`{"number": 123, "message": "test"}`,    // Wrong type for number
		`{number: "123", message: "test"}`,      // Unquoted keys
		`{"number": "123", "message": "test",}`, // Trailing comma
	}

	for i, malformedJSON := range malformedJSONs {
		t.Run(fmt.Sprintf("malformed_%d", i), func(t *testing.T) {
			req := httptest.NewRequest(http.MethodPost, "/api/send", strings.NewReader(malformedJSON))
			req.Header.Set("Content-Type", "application/json")

			rr := httptest.NewRecorder()
			SendHandler(rr, req)

			if rr.Code != http.StatusBadRequest {
				t.Errorf("Expected status 400 for malformed JSON, got %d", rr.Code)
			}

			var response SendResponse
			err := json.NewDecoder(rr.Body).Decode(&response)
			if err != nil {
				t.Fatalf("Failed to decode error response: %v", err)
			}

			if response.Success {
				t.Error("Expected success to be false for malformed JSON")
			}

			if response.Error == "" {
				t.Error("Expected error message for malformed JSON")
			}
		})
	}
}
