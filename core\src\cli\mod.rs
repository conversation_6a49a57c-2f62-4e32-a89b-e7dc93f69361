/*!
# CLI Module

Professional command-line interface for the Wellbot Bridge Service using cliclack.
Provides beautiful interactive prompts for configuration, JID management, and monitoring.
*/

use std::{sync::Arc, time::Duration};

use cliclack::{confirm, input, intro, log, note, outro, select, spinner};
use console::style;
use tokio::time::sleep;

use crate::{
    config::Config,
    error::BridgeResult,
    services::{
        bridge_service::{BridgeService, BridgeServiceConfig},
        jid_authorization::{JidAuthConfig, JidAuthorizationService},
    },
};

/// CLI operation modes
#[derive(Debug, Clone, Copy, Eq, PartialEq)]
pub enum CliMode {
    Interactive,
    Configure,
    Monitor,
    JidManagement,
    ServiceControl,
}

/// CLI configuration options
#[derive(Debug, Clone)]
pub struct CliConfig {
    pub enable_background_tasks: bool,
    pub websocket_auto_reconnect: bool,
    pub ai_processing_enabled: bool,
    pub max_concurrent_ai_requests: usize,
    pub allowed_jids: Vec<String>,
    pub jid_storage_path: String,
}

impl Default for CliConfig {
    fn default() -> Self {
        Self {
            enable_background_tasks: true,
            websocket_auto_reconnect: true,
            ai_processing_enabled: true,
            max_concurrent_ai_requests: 5,
            allowed_jids: vec![],
            jid_storage_path: "data/jid_authorization.json".to_string(),
        }
    }
}

/// Main CLI interface for the Wellbot Bridge Service
pub struct WellbotCli {
    config: Config,
    cli_config: CliConfig,
    bridge_service: Option<Arc<BridgeService>>,
    jid_service: Option<Arc<JidAuthorizationService>>,
}

impl WellbotCli {
    /// Create a new CLI instance
    pub fn new() -> BridgeResult<Self> {
        let config = Config::from_env()?;

        Ok(Self {
            config,
            cli_config: CliConfig::default(),
            bridge_service: None,
            jid_service: None,
        })
    }

    /// Run the main CLI interface
    pub async fn run(&mut self) -> BridgeResult<()> {
        // Set up Ctrl-C handler for graceful exit
        let (shutdown_tx, mut shutdown_rx) = tokio::sync::broadcast::channel(1);
        let shutdown_tx_clone = shutdown_tx.clone();

        ctrlc::set_handler(move || {
            println!("\n🛑 Received interrupt signal - shutting down gracefully...");
            let _ = shutdown_tx_clone.send(());
        })
        .expect("Error setting Ctrl-C handler");

        // Clear screen and show beautiful intro
        cliclack::clear_screen()?;

        intro(style(" 🤖 Wellbot Bridge Service CLI ").on_cyan().black())?;

        log::info("Welcome to the Wellbot Bridge Service interactive interface!")?;
        log::remark("This CLI provides comprehensive management for WhatsApp AI integration")?;

        // Show service requirements
        note(
            "📋 Service Requirements",
            "• Chat-port service running at: ws://localhost:8081/ws\n• AI service (genuis) running at: http://localhost:8000\n• Proper JID authorization configuration",
        )?;

        // Main CLI loop
        loop {
            tokio::select! {
                _ = shutdown_rx.recv() => {
                    log::info("Received shutdown signal")?;
                    break;
                }
                cli_result = self.run_main_menu() => {
                    match cli_result {
                        Ok(should_continue) => {
                            if !should_continue {
                                break;
                            }
                        }
                        Err(e) => {
                            log::error(&format!("CLI error: {}", e))?;

                            let retry = confirm("Would you like to continue?")
                                .initial_value(true)
                                .interact()?;

                            if !retry {
                                break;
                            }
                        }
                    }
                }
            }
        }

        // Graceful shutdown
        self.shutdown().await?;

        outro("🎉 Thank you for using Wellbot Bridge Service!")?;
        Ok(())
    }

    /// Run the main menu interface
    async fn run_main_menu(&mut self) -> BridgeResult<bool> {
        let mode = select("What would you like to do?")
            .initial_value(CliMode::Interactive)
            .item(
                CliMode::Configure,
                "⚙️  Configure Service",
                "Set up bridge service parameters and JID authorization",
            )
            .item(
                CliMode::JidManagement,
                "🔐 Manage JIDs",
                "Add, remove, and monitor authorized WhatsApp accounts",
            )
            .item(
                CliMode::ServiceControl,
                "🚀 Service Control",
                "Start, stop, and monitor the bridge service",
            )
            .item(
                CliMode::Monitor,
                "📊 Real-time Monitoring",
                "Monitor service statistics and message processing",
            )
            .item(
                CliMode::Interactive,
                "🎯 Interactive Demo",
                "Run interactive demonstrations and tests",
            )
            .interact()?;

        match mode {
            CliMode::Configure => self.run_configuration_menu().await,
            CliMode::JidManagement => self.run_jid_management_menu().await,
            CliMode::ServiceControl => self.run_service_control_menu().await,
            CliMode::Monitor => self.run_monitoring_menu().await,
            CliMode::Interactive => self.run_interactive_demo().await,
        }
    }

    /// Run configuration menu
    async fn run_configuration_menu(&mut self) -> BridgeResult<bool> {
        log::info("🔧 Entering configuration mode...")?;

        // Background tasks configuration
        self.cli_config.enable_background_tasks = confirm("Enable background message processing?")
            .initial_value(self.cli_config.enable_background_tasks)
            .interact()?;

        if self.cli_config.enable_background_tasks {
            log::success("✅ Background processing will be enabled")?;
        } else {
            log::warning("⚠️  Background processing will be disabled")?;
        }

        // WebSocket auto-reconnect
        self.cli_config.websocket_auto_reconnect = confirm("Enable WebSocket auto-reconnect?")
            .initial_value(self.cli_config.websocket_auto_reconnect)
            .interact()?;

        // AI processing
        self.cli_config.ai_processing_enabled = confirm("Enable AI message processing?")
            .initial_value(self.cli_config.ai_processing_enabled)
            .interact()?;

        if self.cli_config.ai_processing_enabled {
            let max_concurrent_str = input("Maximum concurrent AI requests:")
                .default_input(&self.cli_config.max_concurrent_ai_requests.to_string())
                .interact()?;

            self.cli_config.max_concurrent_ai_requests = max_concurrent_str
                .parse()
                .unwrap_or(self.cli_config.max_concurrent_ai_requests);
        }

        // JID storage path
        self.cli_config.jid_storage_path = input("JID authorization storage path:")
            .default_input(&self.cli_config.jid_storage_path)
            .interact()?;

        log::success("✅ Configuration updated successfully!")?;

        // Ask if user wants to save configuration
        let save_config = confirm("Save configuration to file?")
            .initial_value(true)
            .interact()?;

        if save_config {
            // TODO: Implement configuration saving
            log::info("💾 Configuration saved (feature coming soon)")?;
        }

        Ok(true)
    }

    /// Run JID management menu
    async fn run_jid_management_menu(&mut self) -> BridgeResult<bool> {
        log::info("🔐 Entering JID management mode...")?;

        // Initialize JID service if not already done
        if self.jid_service.is_none() {
            let spinner = spinner();
            spinner.start("🔄 Initializing JID authorization service...");

            let jid_config = JidAuthConfig {
                storage_path: self.cli_config.jid_storage_path.clone().into(),
                ..Default::default()
            };

            let jid_service = JidAuthorizationService::new(jid_config).await?;
            self.jid_service = Some(Arc::new(jid_service));

            spinner.stop("✅ JID authorization service initialized");
        }

        let jid_service = self.jid_service.as_ref().unwrap();

        // Show current JIDs
        let current_jids = jid_service.get_authorized_jids().await;
        if current_jids.is_empty() {
            log::warning("⚠️  No authorized JIDs configured")?;
        } else {
            log::info(&format!(
                "📋 Current authorized JIDs: {}",
                current_jids.len()
            ))?;
            for jid in &current_jids {
                log::remark(&format!("  • {}", jid))?;
            }
        }

        // JID management options
        let action = select("JID Management Action:")
            .item("add", "➕ Add JID", "Add a new authorized WhatsApp account")
            .item(
                "remove",
                "➖ Remove JID",
                "Remove an authorized WhatsApp account",
            )
            .item("list", "📋 List All", "Show all JID entries with details")
            .item(
                "stats",
                "📊 Statistics",
                "Show JID authorization statistics",
            )
            .item("cleanup", "🧹 Cleanup", "Remove inactive JIDs")
            .interact()?;

        match action {
            "add" => self.add_jid_interactive(jid_service).await?,
            "remove" => self.remove_jid_interactive(jid_service).await?,
            "list" => self.list_jids_detailed(jid_service).await?,
            "stats" => self.show_jid_stats(jid_service).await?,
            "cleanup" => self.cleanup_jids_interactive(jid_service).await?,
            _ => {}
        }

        Ok(true)
    }

    /// Add JID interactively
    async fn add_jid_interactive(
        &mut self,
        jid_service: &JidAuthorizationService,
    ) -> BridgeResult<()> {
        let jid = input("Enter WhatsApp JID (phone number or account ID):")
            .placeholder("e.g., <EMAIL>")
            .interact()?;

        let display_name = input("Enter display name (optional):")
            .placeholder("e.g., John Doe")
            .interact()?;

        let display_name = if display_name.trim().is_empty() {
            None
        } else {
            Some(display_name)
        };

        let spinner = spinner();
        spinner.start("➕ Adding JID to authorized list...");

        let was_new = jid_service.add_jid(jid.clone(), display_name).await?;

        if was_new {
            spinner.stop("✅ JID added successfully!");
            self.cli_config.allowed_jids.push(jid);
        } else {
            spinner.stop("✅ JID reactivated successfully!");
        }

        Ok(())
    }

    /// Remove JID interactively
    async fn remove_jid_interactive(
        &mut self,
        jid_service: &JidAuthorizationService,
    ) -> BridgeResult<()> {
        let current_jids = jid_service.get_authorized_jids().await;

        if current_jids.is_empty() {
            log::warning("⚠️  No authorized JIDs to remove")?;
            return Ok(());
        }

        let jid_to_remove = select("Select JID to remove:")
            .items(
                &current_jids
                    .iter()
                    .map(|jid| (jid.as_str(), jid.as_str(), ""))
                    .collect::<Vec<_>>(),
            )
            .interact()?;

        let confirm_removal = confirm(&format!(
            "Are you sure you want to remove '{}'?",
            jid_to_remove
        ))
        .initial_value(false)
        .interact()?;

        if confirm_removal {
            let spinner = spinner();
            spinner.start("➖ Removing JID from authorized list...");

            let was_removed = jid_service.remove_jid(jid_to_remove).await?;

            if was_removed {
                spinner.stop("✅ JID removed successfully!");
                self.cli_config
                    .allowed_jids
                    .retain(|jid| jid != jid_to_remove);
            } else {
                spinner.stop("⚠️  JID was not found in authorized list");
            }
        }

        Ok(())
    }

    /// List JIDs with detailed information
    async fn list_jids_detailed(&self, jid_service: &JidAuthorizationService) -> BridgeResult<()> {
        let spinner = spinner();
        spinner.start("📋 Loading JID details...");

        let entries = jid_service.get_all_entries().await;
        spinner.stop("✅ JID details loaded");

        if entries.is_empty() {
            log::warning("⚠️  No JID entries found")?;
            return Ok(());
        }

        log::info(&format!("📋 Found {} JID entries:", entries.len()))?;

        for entry in entries {
            let status = if entry.is_active {
                "🟢 Active"
            } else {
                "🔴 Inactive"
            };
            let display_name = entry.display_name.as_deref().unwrap_or("N/A");
            let message_count = entry.message_count;
            let last_message = entry
                .last_message_at
                .map(|ts| format!("{} ago", format_duration_since(ts)))
                .unwrap_or_else(|| "Never".to_string());

            log::remark(&format!(
                "  {} {} ({})\n    Messages: {} | Last: {} | Added: {}",
                status,
                entry.jid,
                display_name,
                message_count,
                last_message,
                format_duration_since(entry.added_at)
            ))?;
        }

        Ok(())
    }

    /// Show JID authorization statistics
    async fn show_jid_stats(&self, jid_service: &JidAuthorizationService) -> BridgeResult<()> {
        let spinner = spinner();
        spinner.start("📊 Loading statistics...");

        let stats = jid_service.get_stats().await;
        spinner.stop("✅ Statistics loaded");

        log::info("📊 JID Authorization Statistics:")?;
        log::remark(&format!("  Total JIDs: {}", stats.total_jids))?;
        log::remark(&format!("  Active JIDs: {}", stats.active_jids))?;
        log::remark(&format!("  Inactive JIDs: {}", stats.inactive_jids))?;
        log::remark(&format!(
            "  Messages Processed: {}",
            stats.total_messages_processed
        ))?;

        if let Some(last_save) = stats.last_save_time {
            log::remark(&format!(
                "  Last Save: {}",
                format_duration_since(last_save)
            ))?;
        }

        if let Some(last_cleanup) = stats.last_cleanup_time {
            log::remark(&format!(
                "  Last Cleanup: {}",
                format_duration_since(last_cleanup)
            ))?;
        }

        Ok(())
    }

    /// Cleanup inactive JIDs interactively
    async fn cleanup_jids_interactive(
        &self,
        jid_service: &JidAuthorizationService,
    ) -> BridgeResult<()> {
        let confirm_cleanup =
            confirm("This will remove JIDs that haven't been active recently. Continue?")
                .initial_value(false)
                .interact()?;

        if !confirm_cleanup {
            return Ok(());
        }

        let spinner = spinner();
        spinner.start("🧹 Cleaning up inactive JIDs...");

        let removed_count = jid_service.cleanup_inactive_jids().await?;

        if removed_count > 0 {
            spinner.stop(&format!(
                "✅ Cleanup completed - removed {} inactive JIDs",
                removed_count
            ));
        } else {
            spinner.stop("✅ Cleanup completed - no inactive JIDs found");
        }

        Ok(())
    }

    /// Run service control menu
    async fn run_service_control_menu(&mut self) -> BridgeResult<bool> {
        log::info("🚀 Entering service control mode...")?;

        let action = select("Service Control Action:")
            .item(
                "start",
                "🚀 Start Service",
                "Start the bridge service with current configuration",
            )
            .item("stop", "🛑 Stop Service", "Stop the running bridge service")
            .item(
                "restart",
                "🔄 Restart Service",
                "Restart the bridge service",
            )
            .item(
                "status",
                "📊 Service Status",
                "Check service health and status",
            )
            .item(
                "test",
                "🧪 Test Connection",
                "Test connections to external services",
            )
            .interact()?;

        match action {
            "start" => self.start_service().await?,
            "stop" => self.stop_service().await?,
            "restart" => {
                self.stop_service().await?;
                sleep(Duration::from_secs(2)).await;
                self.start_service().await?;
            }
            "status" => self.show_service_status().await?,
            "test" => self.test_connections().await?,
            _ => {}
        }

        Ok(true)
    }

    /// Start the bridge service
    async fn start_service(&mut self) -> BridgeResult<()> {
        if self.bridge_service.is_some() {
            log::warning("⚠️  Service is already running")?;
            return Ok(());
        }

        let spinner = spinner();
        spinner.start("🚀 Starting Wellbot Bridge Service...");

        // Create bridge service configuration
        let bridge_config = BridgeServiceConfig {
            allowed_jids: self.cli_config.allowed_jids.clone(),
            enable_background_processing: self.cli_config.enable_background_tasks,
            websocket_auto_reconnect: self.cli_config.websocket_auto_reconnect,
            ai_processing_enabled: self.cli_config.ai_processing_enabled,
            max_concurrent_ai_requests: self.cli_config.max_concurrent_ai_requests,
        };

        // Initialize bridge service
        let bridge_service =
            BridgeService::new_with_runtime_config(self.config.clone(), bridge_config).await?;

        // Start the service
        bridge_service.start().await?;

        self.bridge_service = Some(Arc::new(bridge_service));

        spinner.stop("✅ Bridge service started successfully!");
        log::success("🎉 Wellbot Bridge Service is now running")?;

        Ok(())
    }

    /// Stop the bridge service
    async fn stop_service(&mut self) -> BridgeResult<()> {
        if let Some(service) = self.bridge_service.take() {
            let spinner = spinner();
            spinner.start("🛑 Stopping bridge service...");

            service.stop().await?;

            spinner.stop("✅ Bridge service stopped successfully!");
        } else {
            log::warning("⚠️  Service is not running")?;
        }

        Ok(())
    }

    /// Show service status
    async fn show_service_status(&self) -> BridgeResult<()> {
        if let Some(service) = &self.bridge_service {
            let spinner = spinner();
            spinner.start("📊 Checking service status...");

            let health_ok = service.health_check().await.unwrap_or(false);
            let stats = service.get_stats().await;

            spinner.stop("✅ Status check completed");

            log::info("📊 Bridge Service Status:")?;
            log::remark(&format!(
                "  Health: {}",
                if health_ok {
                    "🟢 Healthy"
                } else {
                    "🔴 Unhealthy"
                }
            ))?;
            log::remark(&format!(
                "  Messages Processed: {}",
                stats.messages_processed
            ))?;
            log::remark(&format!(
                "  Authorized Messages: {}",
                stats.messages_authorized
            ))?;
            log::remark(&format!(
                "  Unauthorized Messages: {}",
                stats.messages_unauthorized
            ))?;
            log::remark(&format!("  AI Requests: {}", stats.ai_requests_sent))?;
            log::remark(&format!("  AI Responses: {}", stats.ai_responses_received))?;
            log::remark(&format!(
                "  WebSocket Reconnections: {}",
                stats.websocket_reconnections
            ))?;
            log::remark(&format!("  Errors: {}", stats.errors_encountered))?;
        } else {
            log::warning("⚠️  Service is not running")?;
        }

        Ok(())
    }

    /// Test connections to external services
    async fn test_connections(&self) -> BridgeResult<()> {
        log::info("🧪 Testing connections to external services...")?;

        // Test AI service
        let spinner = spinner();
        spinner.start("🤖 Testing AI service connection...");

        let ai_client = crate::services::ai_client::AiClient::new(self.config.ai_service.clone())?;
        let ai_healthy = ai_client.health_check().await.unwrap_or(false);

        if ai_healthy {
            spinner.stop("✅ AI service connection: OK");
        } else {
            spinner.stop("❌ AI service connection: FAILED");
        }

        // Test chat-port service (basic connectivity)
        let spinner = spinner();
        spinner.start("💬 Testing chat-port service connection...");

        let chat_client =
            crate::services::chat_port_client::ChatPortClient::new(self.config.chat_port.clone())?;
        // Note: We can't easily test WebSocket without actually connecting, so we'll just validate config
        spinner.stop("✅ Chat-port service configuration: OK");

        log::info("🧪 Connection tests completed")?;
        Ok(())
    }

    /// Run monitoring menu
    async fn run_monitoring_menu(&mut self) -> BridgeResult<bool> {
        log::info("📊 Entering monitoring mode...")?;

        if self.bridge_service.is_none() {
            log::warning("⚠️  Bridge service is not running. Start the service first.")?;
            return Ok(true);
        }

        let service = self.bridge_service.as_ref().unwrap();

        let monitor_duration = select("Monitoring Duration:")
            .item(30, "30 seconds", "Quick monitoring session")
            .item(60, "1 minute", "Standard monitoring session")
            .item(300, "5 minutes", "Extended monitoring session")
            .item(0, "Continuous", "Monitor until interrupted")
            .interact()?;

        let spinner = spinner();
        spinner.start("📊 Starting real-time monitoring...");

        let start_time = std::time::Instant::now();
        let mut last_stats = service.get_stats().await;

        loop {
            sleep(Duration::from_secs(5)).await;

            let current_stats = service.get_stats().await;
            let elapsed = start_time.elapsed().as_secs();

            // Calculate deltas
            let messages_delta = current_stats.messages_processed - last_stats.messages_processed;
            let ai_requests_delta = current_stats.ai_requests_sent - last_stats.ai_requests_sent;
            let errors_delta = current_stats.errors_encountered - last_stats.errors_encountered;

            spinner.stop(&format!(
                "📊 Monitoring ({}s) - Messages: +{}, AI: +{}, Errors: +{}",
                elapsed, messages_delta, ai_requests_delta, errors_delta
            ));

            last_stats = current_stats;

            // Check if we should continue
            if monitor_duration > 0 && elapsed >= monitor_duration {
                break;
            }

            spinner.start(&format!("📊 Monitoring... ({}s elapsed)", elapsed));
        }

        log::success("✅ Monitoring session completed")?;
        Ok(true)
    }

    /// Run interactive demo
    async fn run_interactive_demo(&mut self) -> BridgeResult<bool> {
        log::info("🎯 Entering interactive demo mode...")?;

        let demo_type = select("Select Demo Type:")
            .item(
                "message_flow",
                "📨 Message Flow",
                "Demonstrate complete message processing flow",
            )
            .item(
                "jid_filtering",
                "🔐 JID Filtering",
                "Show JID authorization in action",
            )
            .item(
                "ai_integration",
                "🤖 AI Integration",
                "Test AI response generation",
            )
            .item(
                "error_handling",
                "⚠️  Error Handling",
                "Demonstrate error scenarios",
            )
            .interact()?;

        match demo_type {
            "message_flow" => self.demo_message_flow().await?,
            "jid_filtering" => self.demo_jid_filtering().await?,
            "ai_integration" => self.demo_ai_integration().await?,
            "error_handling" => self.demo_error_handling().await?,
            _ => {}
        }

        Ok(true)
    }

    /// Demo message flow
    async fn demo_message_flow(&self) -> BridgeResult<()> {
        log::info("📨 Starting message flow demonstration...")?;

        note(
            "📨 Message Flow Demo",
            "This demo shows how messages flow from WhatsApp through the bridge to AI and back.\n\nFlow: WhatsApp → Bridge → JID Check → AI Processing → Response → WhatsApp",
        )?;

        // TODO: Implement actual message flow demo
        log::info("🚧 Message flow demo implementation coming soon...")?;

        Ok(())
    }

    /// Demo JID filtering
    async fn demo_jid_filtering(&self) -> BridgeResult<()> {
        log::info("🔐 Starting JID filtering demonstration...")?;

        note(
            "🔐 JID Filtering Demo",
            "This demo shows how the bridge filters messages based on authorized JIDs.\n\nOnly messages from authorized WhatsApp accounts are processed.",
        )?;

        // TODO: Implement JID filtering demo
        log::info("🚧 JID filtering demo implementation coming soon...")?;

        Ok(())
    }

    /// Demo AI integration
    async fn demo_ai_integration(&self) -> BridgeResult<()> {
        log::info("🤖 Starting AI integration demonstration...")?;

        note(
            "🤖 AI Integration Demo",
            "This demo shows how the bridge integrates with the AI service for response generation.\n\nMessages are sent to the AI service and responses are generated.",
        )?;

        // TODO: Implement AI integration demo
        log::info("🚧 AI integration demo implementation coming soon...")?;

        Ok(())
    }

    /// Demo error handling
    async fn demo_error_handling(&self) -> BridgeResult<()> {
        log::info("⚠️  Starting error handling demonstration...")?;

        note(
            "⚠️  Error Handling Demo",
            "This demo shows how the bridge handles various error scenarios gracefully.\n\nIncludes network errors, AI service failures, and invalid messages.",
        )?;

        // TODO: Implement error handling demo
        log::info("🚧 Error handling demo implementation coming soon...")?;

        Ok(())
    }

    /// Graceful shutdown
    async fn shutdown(&mut self) -> BridgeResult<()> {
        log::info("🛑 Shutting down CLI...")?;

        // Stop bridge service
        if self.bridge_service.is_some() {
            self.stop_service().await?;
        }

        // Shutdown JID service
        if let Some(jid_service) = &self.jid_service {
            jid_service.shutdown().await?;
        }

        log::success("✅ CLI shutdown completed")?;
        Ok(())
    }
}

/// Format duration since timestamp as human-readable string
fn format_duration_since(timestamp: u64) -> String {
    use std::time::{SystemTime, UNIX_EPOCH};

    let now = SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap()
        .as_secs();

    let elapsed = now.saturating_sub(timestamp);

    if elapsed < 60 {
        format!("{}s", elapsed)
    } else if elapsed < 3600 {
        format!("{}m", elapsed / 60)
    } else if elapsed < 86400 {
        format!("{}h", elapsed / 3600)
    } else {
        format!("{}d", elapsed / 86400)
    }
}
