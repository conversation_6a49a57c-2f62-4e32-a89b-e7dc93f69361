package websocket

import (
	"context"
	"net/http"
	"sync"
	"time"

	"chatport-go/internal/logger"

	"github.com/gorilla/websocket"
)

const (
	// Time allowed to write a message to the peer
	writeWait = 10 * time.Second

	// Time allowed to read the next pong message from the peer
	pongWait = 60 * time.Second

	// Send pings to peer with this period. Must be less than pongWait
	pingPeriod = (pongWait * 9) / 10

	// Maximum message size allowed from peer
	maxMessageSize = 512

	// Buffer size for client channels
	channelBufferSize = 256
)

// Client represents a WebSocket client connection
type Client struct {
	// The WebSocket connection
	conn *websocket.Conn

	// Buffered channel of outbound messages
	send chan []byte

	// Client ID
	id string

	// Client subscriptions
	subscriptions map[SubscriptionType]bool

	// Mutex for thread-safe access to subscriptions
	subscriptionsMu sync.RWMutex

	// Context for cancellation
	ctx    context.Context
	cancel context.CancelFunc

	// Connection metadata
	remoteAddr  string
	userAgent   string
	connectedAt time.Time

	// Last activity timestamp
	lastActivity time.Time
	activityMu   sync.RWMutex

	// Hub reference
	hub *Hub
}

// NewClient creates a new WebSocket client
func NewClient(conn *websocket.Conn, hub *Hub, id string, r *http.Request) *Client {
	ctx, cancel := context.WithCancel(context.Background())

	client := &Client{
		conn:          conn,
		send:          make(chan []byte, channelBufferSize),
		id:            id,
		subscriptions: make(map[SubscriptionType]bool),
		ctx:           ctx,
		cancel:        cancel,
		remoteAddr:    conn.RemoteAddr().String(),
		userAgent:     r.Header.Get("User-Agent"),
		connectedAt:   time.Now(),
		lastActivity:  time.Now(),
		hub:           hub,
	}

	return client
}

// ID returns the client ID
func (c *Client) ID() string {
	return c.id
}

// RemoteAddr returns the client's remote address
func (c *Client) RemoteAddr() string {
	return c.remoteAddr
}

// UserAgent returns the client's user agent
func (c *Client) UserAgent() string {
	return c.userAgent
}

// ConnectedAt returns when the client connected
func (c *Client) ConnectedAt() time.Time {
	return c.connectedAt
}

// LastActivity returns the last activity timestamp
func (c *Client) LastActivity() time.Time {
	c.activityMu.RLock()
	defer c.activityMu.RUnlock()
	return c.lastActivity
}

// updateActivity updates the last activity timestamp
func (c *Client) updateActivity() {
	c.activityMu.Lock()
	defer c.activityMu.Unlock()
	c.lastActivity = time.Now()
}

// IsSubscribed checks if the client is subscribed to a specific type
func (c *Client) IsSubscribed(subType SubscriptionType) bool {
	c.subscriptionsMu.RLock()
	defer c.subscriptionsMu.RUnlock()

	// Check for specific subscription or "all" subscription
	return c.subscriptions[subType] || c.subscriptions[SubscriptionAll]
}

// Subscribe adds a subscription for the client
func (c *Client) Subscribe(subType SubscriptionType) {
	c.subscriptionsMu.Lock()
	defer c.subscriptionsMu.Unlock()

	c.subscriptions[subType] = true
	logger.Debugf("Client %s subscribed to %s", c.id, subType)
}

// Unsubscribe removes a subscription for the client
func (c *Client) Unsubscribe(subType SubscriptionType) {
	c.subscriptionsMu.Lock()
	defer c.subscriptionsMu.Unlock()

	delete(c.subscriptions, subType)
	logger.Debugf("Client %s unsubscribed from %s", c.id, subType)
}

// GetSubscriptions returns a copy of current subscriptions
func (c *Client) GetSubscriptions() map[SubscriptionType]bool {
	c.subscriptionsMu.RLock()
	defer c.subscriptionsMu.RUnlock()

	subs := make(map[SubscriptionType]bool)
	for k, v := range c.subscriptions {
		subs[k] = v
	}
	return subs
}

// SendMessage sends a message to the client
func (c *Client) SendMessage(message *Message) error {
	data, err := message.ToJSON()
	if err != nil {
		return err
	}

	select {
	case c.send <- data:
		return nil
	case <-c.ctx.Done():
		return c.ctx.Err()
	default:
		// Channel is full, client is slow
		logger.Warnf("Client %s send channel is full, dropping message", c.id)
		return ErrClientSlow
	}
}

// Close closes the client connection
func (c *Client) Close() {
	if c.cancel != nil {
		c.cancel()
	}
	if c.send != nil {
		close(c.send)
	}
	if c.conn != nil {
		c.conn.Close()
	}
	logger.Debugf("Client %s connection closed", c.id)
}

// readPump pumps messages from the WebSocket connection to the hub
func (c *Client) readPump() {
	defer func() {
		c.hub.unregister <- c
		c.conn.Close()
	}()

	c.conn.SetReadLimit(maxMessageSize)
	c.conn.SetReadDeadline(time.Now().Add(pongWait))
	c.conn.SetPongHandler(func(string) error {
		c.conn.SetReadDeadline(time.Now().Add(pongWait))
		c.updateActivity()
		return nil
	})

	for {
		select {
		case <-c.ctx.Done():
			return
		default:
			_, messageData, err := c.conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					logger.Errorf("WebSocket error for client %s: %v", c.id, err)
				}
				return
			}

			c.updateActivity()

			// Parse and handle the message
			message, err := ParseMessage(messageData)
			if err != nil {
				logger.Errorf("Failed to parse message from client %s: %v", c.id, err)
				c.sendError("PARSE_ERROR", "Failed to parse message", err.Error())
				continue
			}

			c.handleMessage(message)
		}
	}
}

// writePump pumps messages from the hub to the WebSocket connection
func (c *Client) writePump() {
	ticker := time.NewTicker(pingPeriod)
	defer func() {
		ticker.Stop()
		c.conn.Close()
	}()

	for {
		select {
		case <-c.ctx.Done():
			return
		case message, ok := <-c.send:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if !ok {
				// The hub closed the channel
				c.conn.WriteMessage(websocket.CloseMessage, []byte{})
				return
			}

			w, err := c.conn.NextWriter(websocket.TextMessage)
			if err != nil {
				return
			}
			w.Write(message)

			// Add queued messages to the current WebSocket message
			n := len(c.send)
			for i := 0; i < n; i++ {
				w.Write([]byte{'\n'})
				w.Write(<-c.send)
			}

			if err := w.Close(); err != nil {
				return
			}

		case <-ticker.C:
			c.conn.SetWriteDeadline(time.Now().Add(writeWait))
			if err := c.conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				return
			}
		}
	}
}

// handleMessage handles incoming messages from the client
func (c *Client) handleMessage(message *Message) {
	switch message.Type {
	case MessageTypeSubscribe, MessageTypeUnsubscribe:
		c.handleSubscription(message)
	case MessageTypeHeartbeat:
		c.handleHeartbeat(message)
	case MessageTypePing:
		c.handlePing(message)
	default:
		logger.Warnf("Unknown message type from client %s: %s", c.id, message.Type)
		c.sendError("UNKNOWN_MESSAGE_TYPE", "Unknown message type", string(message.Type))
	}
}

// handleSubscription handles subscription/unsubscription requests
func (c *Client) handleSubscription(message *Message) {
	req, err := ParseSubscriptionRequest(message.Data)
	if err != nil {
		logger.Errorf("Failed to parse subscription request from client %s: %v", c.id, err)
		c.sendError("INVALID_SUBSCRIPTION", "Invalid subscription request", err.Error())
		return
	}

	if !IsValidSubscriptionType(req.Subscription) {
		c.sendError("INVALID_SUBSCRIPTION_TYPE", "Invalid subscription type", string(req.Subscription))
		return
	}

	switch req.Action {
	case "subscribe":
		c.Subscribe(req.Subscription)
	case "unsubscribe":
		c.Unsubscribe(req.Subscription)
	default:
		c.sendError("INVALID_ACTION", "Invalid subscription action", req.Action)
	}
}

// handleHeartbeat handles heartbeat messages
func (c *Client) handleHeartbeat(_ *Message) {
	// Just update activity - heartbeat is handled by updateActivity
	c.updateActivity()
}

// handlePing handles ping messages and responds with pong
func (c *Client) handlePing(_ *Message) {
	pongMsg, err := NewMessage(MessageTypePong, nil)
	if err != nil {
		logger.Errorf("Failed to create pong message for client %s: %v", c.id, err)
		return
	}

	c.SendMessage(pongMsg)
}

// sendError sends an error message to the client
func (c *Client) sendError(code, message, details string) {
	errorMsg, err := NewErrorMessage(code, message, details)
	if err != nil {
		logger.Errorf("Failed to create error message for client %s: %v", c.id, err)
		return
	}

	c.SendMessage(errorMsg)
}

// Run starts the client's read and write pumps
func (c *Client) Run() {
	go c.writePump()
	go c.readPump()
}

// Stats returns client statistics
func (c *Client) Stats() map[string]interface{} {
	return map[string]interface{}{
		"id":            c.id,
		"remote_addr":   c.remoteAddr,
		"user_agent":    c.userAgent,
		"connected_at":  c.connectedAt,
		"last_activity": c.LastActivity(),
		"subscriptions": c.GetSubscriptions(),
		"uptime":        time.Since(c.connectedAt).String(),
	}
}
