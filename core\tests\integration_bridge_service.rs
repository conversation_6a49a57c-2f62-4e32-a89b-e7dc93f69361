/*!
# Integration Tests for Bridge Service

Comprehensive integration tests that verify the complete message flow
from WhatsApp through the bridge to AI client and back.
*/

use std::{sync::Arc, time::Duration};

use tokio::time::timeout;
use wellbot_bridge::{
    config::{Config, AiServiceConfig, ChatPortConfig, BridgeConfig},
    services::{
        bridge_service::{BridgeService, BridgeServiceConfig},
        jid_authorization::{JidAuthConfig, JidAuthorizationService},
    },
    types::IncomingWhatsAppData,
};

/// Test configuration for integration tests
fn create_test_config() -> Config {
    Config {
        ai_service: AiServiceConfig {
            base_url: "http://localhost:8000".to_string(),
            timeout_secs: 10,
        },
        chat_port: ChatPortConfig {
            api_base_url: "http://localhost:8081".to_string(),
            websocket_url: "ws://localhost:8081/ws".to_string(),
            connection_timeout_secs: 5,
            max_reconnect_attempts: 3,
            reconnect_delay_secs: 1,
            heartbeat_interval_secs: 10,
        },
        bridge: BridgeConfig {
            max_concurrent_requests: 5,
            request_timeout_secs: 30,
            rate_limit_per_minute: 60,
        },
    }
}

/// Test bridge service initialization
#[tokio::test]
async fn test_bridge_service_initialization() {
    let config = create_test_config();
    
    let bridge_config = BridgeServiceConfig {
        allowed_jids: vec!["<EMAIL>".to_string()],
        enable_background_processing: false, // Disable for testing
        websocket_auto_reconnect: false,     // Disable for testing
        ai_processing_enabled: true,
        max_concurrent_ai_requests: 3,
    };

    let result = BridgeService::new_with_runtime_config(config, bridge_config).await;
    assert!(result.is_ok(), "Bridge service should initialize successfully");

    let bridge_service = result.unwrap();
    
    // Test JID management
    let jids = bridge_service.get_allowed_jids().await;
    assert_eq!(jids.len(), 1);
    assert_eq!(jids[0], "<EMAIL>");
}

/// Test JID authorization functionality
#[tokio::test]
async fn test_jid_authorization() {
    let jid_config = JidAuthConfig {
        storage_path: std::env::temp_dir().join("test_jids.json"),
        auto_save_interval_secs: 0, // Disable auto-save for testing
        backup_retention_days: 7,
        enable_activity_tracking: true,
        max_inactive_days: Some(30),
    };

    let jid_service = JidAuthorizationService::new(jid_config).await;
    assert!(jid_service.is_ok(), "JID service should initialize successfully");

    let jid_service = jid_service.unwrap();

    // Test adding JID
    let result = jid_service.add_jid(
        "<EMAIL>".to_string(),
        Some("Test User".to_string()),
    ).await;
    assert!(result.is_ok(), "Should add JID successfully");

    // Test authorization check
    let is_authorized = jid_service.is_authorized("<EMAIL>").await;
    assert!(is_authorized, "JID should be authorized");

    let is_not_authorized = jid_service.is_authorized("<EMAIL>").await;
    assert!(!is_not_authorized, "Unknown JID should not be authorized");

    // Test getting authorized JIDs
    let jids = jid_service.get_authorized_jids().await;
    assert_eq!(jids.len(), 1);
    assert_eq!(jids[0], "<EMAIL>");

    // Test statistics
    let stats = jid_service.get_stats().await;
    assert_eq!(stats.total_jids, 1);
    assert_eq!(stats.active_jids, 1);
    assert_eq!(stats.inactive_jids, 0);

    // Test removing JID
    let result = jid_service.remove_jid("<EMAIL>").await;
    assert!(result.is_ok(), "Should remove JID successfully");

    let is_authorized_after_removal = jid_service.is_authorized("<EMAIL>").await;
    assert!(!is_authorized_after_removal, "JID should not be authorized after removal");

    // Cleanup
    let _ = jid_service.shutdown().await;
}

/// Test message processing with authorization
#[tokio::test]
async fn test_message_processing_authorization() {
    let config = create_test_config();
    
    let bridge_config = BridgeServiceConfig {
        allowed_jids: vec!["<EMAIL>".to_string()],
        enable_background_processing: false,
        websocket_auto_reconnect: false,
        ai_processing_enabled: false, // Disable AI for this test
        max_concurrent_ai_requests: 1,
    };

    let bridge_service = BridgeService::new_with_runtime_config(config, bridge_config).await;
    assert!(bridge_service.is_ok(), "Bridge service should initialize");

    let bridge_service = bridge_service.unwrap();

    // Test authorized message
    let authorized_message = IncomingWhatsAppData {
        from: "<EMAIL>".to_string(),
        message: "Hello, this is a test message".to_string(),
        message_id: "test_msg_1".to_string(),
        timestamp: chrono::Utc::now(),
    };

    let result = timeout(
        Duration::from_secs(5),
        bridge_service.process_whatsapp_message(authorized_message)
    ).await;

    // Note: This might fail if external services aren't available, which is expected in CI
    match result {
        Ok(Ok(_)) => {
            // Message processed successfully
            let stats = bridge_service.get_stats().await;
            assert!(stats.messages_processed > 0);
            assert!(stats.messages_authorized > 0);
        }
        Ok(Err(_)) | Err(_) => {
            // Expected if external services aren't available
            println!("Message processing failed (expected in test environment)");
        }
    }

    // Test unauthorized message
    let unauthorized_message = IncomingWhatsAppData {
        from: "<EMAIL>".to_string(),
        message: "This should be blocked".to_string(),
        message_id: "test_msg_2".to_string(),
        timestamp: chrono::Utc::now(),
    };

    let result = timeout(
        Duration::from_secs(5),
        bridge_service.process_whatsapp_message(unauthorized_message)
    ).await;

    // This should fail due to authorization
    match result {
        Ok(Err(_)) => {
            // Expected - unauthorized message should be rejected
            let stats = bridge_service.get_stats().await;
            assert!(stats.messages_unauthorized > 0);
        }
        _ => {
            // Unexpected success or timeout
            panic!("Unauthorized message should be rejected");
        }
    }
}

/// Test bridge service statistics
#[tokio::test]
async fn test_bridge_service_statistics() {
    let config = create_test_config();
    
    let bridge_config = BridgeServiceConfig {
        allowed_jids: vec!["<EMAIL>".to_string()],
        enable_background_processing: false,
        websocket_auto_reconnect: false,
        ai_processing_enabled: false,
        max_concurrent_ai_requests: 1,
    };

    let bridge_service = BridgeService::new_with_runtime_config(config, bridge_config).await;
    assert!(bridge_service.is_ok(), "Bridge service should initialize");

    let bridge_service = bridge_service.unwrap();

    // Get initial stats
    let initial_stats = bridge_service.get_stats().await;
    assert_eq!(initial_stats.messages_processed, 0);
    assert_eq!(initial_stats.messages_authorized, 0);
    assert_eq!(initial_stats.messages_unauthorized, 0);

    // Test JID management
    let result = bridge_service.add_allowed_jid("<EMAIL>".to_string()).await;
    assert!(result.is_ok(), "Should add JID successfully");

    let jids = bridge_service.get_allowed_jids().await;
    assert_eq!(jids.len(), 2); // Original + new JID

    let result = bridge_service.remove_allowed_jid("<EMAIL>").await;
    assert!(result.is_ok(), "Should remove JID successfully");

    let jids_after_removal = bridge_service.get_allowed_jids().await;
    assert_eq!(jids_after_removal.len(), 1); // Back to original
}

/// Test service health checks
#[tokio::test]
async fn test_service_health_checks() {
    let config = create_test_config();
    
    let bridge_config = BridgeServiceConfig {
        allowed_jids: vec!["<EMAIL>".to_string()],
        enable_background_processing: false,
        websocket_auto_reconnect: false,
        ai_processing_enabled: true,
        max_concurrent_ai_requests: 1,
    };

    let bridge_service = BridgeService::new_with_runtime_config(config, bridge_config).await;
    assert!(bridge_service.is_ok(), "Bridge service should initialize");

    let bridge_service = bridge_service.unwrap();

    // Test health check
    let health_result = timeout(
        Duration::from_secs(10),
        bridge_service.health_check()
    ).await;

    match health_result {
        Ok(Ok(healthy)) => {
            // Health check completed
            println!("Health check result: {}", healthy);
            // Note: May be false if external services aren't available
        }
        Ok(Err(e)) => {
            println!("Health check error (expected in test environment): {}", e);
        }
        Err(_) => {
            println!("Health check timeout (expected in test environment)");
        }
    }
}

/// Test concurrent message processing
#[tokio::test]
async fn test_concurrent_message_processing() {
    let config = create_test_config();
    
    let bridge_config = BridgeServiceConfig {
        allowed_jids: vec![
            "<EMAIL>".to_string(),
            "<EMAIL>".to_string(),
            "<EMAIL>".to_string(),
        ],
        enable_background_processing: false,
        websocket_auto_reconnect: false,
        ai_processing_enabled: false, // Disable AI for faster testing
        max_concurrent_ai_requests: 3,
    };

    let bridge_service = Arc::new(
        BridgeService::new_with_runtime_config(config, bridge_config).await.unwrap()
    );

    // Create multiple messages
    let messages = vec![
        IncomingWhatsAppData {
            from: "<EMAIL>".to_string(),
            message: "Message 1".to_string(),
            message_id: "concurrent_msg_1".to_string(),
            timestamp: chrono::Utc::now(),
        },
        IncomingWhatsAppData {
            from: "<EMAIL>".to_string(),
            message: "Message 2".to_string(),
            message_id: "concurrent_msg_2".to_string(),
            timestamp: chrono::Utc::now(),
        },
        IncomingWhatsAppData {
            from: "<EMAIL>".to_string(),
            message: "Message 3".to_string(),
            message_id: "concurrent_msg_3".to_string(),
            timestamp: chrono::Utc::now(),
        },
    ];

    // Process messages concurrently
    let mut handles = vec![];
    for message in messages {
        let service = bridge_service.clone();
        let handle = tokio::spawn(async move {
            timeout(
                Duration::from_secs(5),
                service.process_whatsapp_message(message)
            ).await
        });
        handles.push(handle);
    }

    // Wait for all to complete
    let results = futures::future::join_all(handles).await;
    
    // Check that all tasks completed (though they may have failed due to missing external services)
    assert_eq!(results.len(), 3);
    
    for result in results {
        assert!(result.is_ok(), "Task should complete without panicking");
    }
}
