package websocket

import (
	"context"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
)

func newMockHub() *Hub {
	config := DefaultHubConfig()
	config.MaxClients = 10
	return NewHub(config)
}

func TestNewClient(t *testing.T) {
	// Create a test WebSocket connection
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		upgrader := websocket.Upgrader{}
		conn, err := upgrader.Upgrade(w, r, nil)
		if err != nil {
			t.Fatalf("Failed to upgrade connection: %v", err)
		}
		defer conn.Close()

		hub := newMockHub()
		client := NewClient(conn, hub, "test-client", r)

		// Test client properties
		if client.ID() != "test-client" {
			t.Errorf("Expected client ID 'test-client', got %s", client.ID())
		}

		if client.RemoteAddr() == "" {
			t.Error("Expected remote address to be set")
		}

		if client.ConnectedAt().IsZero() {
			t.Error("Expected connected time to be set")
		}

		if client.LastActivity().IsZero() {
			t.Error("Expected last activity to be set")
		}

		// Test initial subscriptions (should be empty)
		subs := client.GetSubscriptions()
		if len(subs) != 0 {
			t.Errorf("Expected no initial subscriptions, got %d", len(subs))
		}
	}))
	defer server.Close()

	// Connect to the test server
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http")
	_, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		t.Fatalf("Failed to connect to WebSocket: %v", err)
	}
}

func TestClient_Subscriptions(t *testing.T) {
	// Create a mock client for testing subscriptions
	client := &Client{
		id:            "test-client",
		subscriptions: make(map[SubscriptionType]bool),
		ctx:           context.Background(),
		connectedAt:   time.Now(),
		lastActivity:  time.Now(),
	}

	// Test initial state
	if client.IsSubscribed(SubscriptionWhatsApp) {
		t.Error("Expected client not to be subscribed to WhatsApp initially")
	}

	// Test subscription
	client.Subscribe(SubscriptionWhatsApp)
	if !client.IsSubscribed(SubscriptionWhatsApp) {
		t.Error("Expected client to be subscribed to WhatsApp after subscribing")
	}

	// Test "all" subscription
	client.Subscribe(SubscriptionAll)
	if !client.IsSubscribed(SubscriptionMetrics) {
		t.Error("Expected client to be subscribed to metrics when subscribed to all")
	}

	// Test unsubscription
	client.Unsubscribe(SubscriptionWhatsApp)
	// Note: Client is still subscribed via "all" subscription
	if !client.IsSubscribed(SubscriptionWhatsApp) {
		t.Error("Expected client to still be subscribed to WhatsApp via 'all' subscription")
	}

	// Unsubscribe from "all" to test individual unsubscription
	client.Unsubscribe(SubscriptionAll)
	if client.IsSubscribed(SubscriptionWhatsApp) {
		t.Error("Expected client not to be subscribed to WhatsApp after unsubscribing from all")
	}

	// Test getting subscriptions (after unsubscribing from all)
	subs := client.GetSubscriptions()
	if subs[SubscriptionAll] {
		t.Error("Expected 'all' subscription to be removed")
	}
}

func TestClient_SendMessage(t *testing.T) {
	client := &Client{
		id:           "test-client",
		send:         make(chan []byte, 10),
		ctx:          context.Background(),
		connectedAt:  time.Now(),
		lastActivity: time.Now(),
	}

	// Test successful send
	msg, err := NewMessage(MessageTypePing, nil)
	if err != nil {
		t.Fatalf("Failed to create message: %v", err)
	}

	err = client.SendMessage(msg)
	if err != nil {
		t.Errorf("Failed to send message: %v", err)
	}

	// Verify message was queued
	select {
	case data := <-client.send:
		_, err := ParseMessage(data)
		if err != nil {
			t.Errorf("Failed to parse queued message: %v", err)
		}
	case <-time.After(100 * time.Millisecond):
		t.Error("Message was not queued")
	}
}

func TestClient_SendMessage_ChannelFull(t *testing.T) {
	// Create client with small buffer
	client := &Client{
		id:           "test-client",
		send:         make(chan []byte, 1),
		ctx:          context.Background(),
		connectedAt:  time.Now(),
		lastActivity: time.Now(),
	}

	msg, _ := NewMessage(MessageTypePing, nil)

	// Fill the channel
	client.SendMessage(msg)

	// This should fail with ErrClientSlow
	err := client.SendMessage(msg)
	if err != ErrClientSlow {
		t.Errorf("Expected ErrClientSlow, got %v", err)
	}
}

func TestClient_SendMessage_ContextCancelled(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())

	client := &Client{
		id:           "test-client",
		send:         make(chan []byte, 1), // Small buffer to test blocking
		ctx:          ctx,
		connectedAt:  time.Now(),
		lastActivity: time.Now(),
	}

	// Fill the channel first
	msg, _ := NewMessage(MessageTypePing, nil)
	client.SendMessage(msg) // This should succeed

	cancel() // Cancel context

	// This should return context.Canceled due to cancelled context
	err := client.SendMessage(msg)
	if err != context.Canceled {
		t.Errorf("Expected context.Canceled, got %v", err)
	}
}

func TestClient_Stats(t *testing.T) {
	client := &Client{
		id:           "test-client",
		remoteAddr:   "127.0.0.1:12345",
		userAgent:    "Test-Agent/1.0",
		connectedAt:  time.Now(),
		lastActivity: time.Now(),
		subscriptions: map[SubscriptionType]bool{
			SubscriptionWhatsApp: true,
		},
	}

	stats := client.Stats()

	// Check required fields
	if stats["id"] != "test-client" {
		t.Errorf("Expected id 'test-client', got %v", stats["id"])
	}

	if stats["remote_addr"] != "127.0.0.1:12345" {
		t.Errorf("Expected remote_addr '127.0.0.1:12345', got %v", stats["remote_addr"])
	}

	if stats["user_agent"] != "Test-Agent/1.0" {
		t.Errorf("Expected user_agent 'Test-Agent/1.0', got %v", stats["user_agent"])
	}

	if stats["connected_at"] == nil {
		t.Error("Expected connected_at to be present")
	}

	if stats["last_activity"] == nil {
		t.Error("Expected last_activity to be present")
	}

	if stats["subscriptions"] == nil {
		t.Error("Expected subscriptions to be present")
	}

	if stats["uptime"] == nil {
		t.Error("Expected uptime to be present")
	}
}

func TestClient_UpdateActivity(t *testing.T) {
	client := &Client{
		id:           "test-client",
		connectedAt:  time.Now(),
		lastActivity: time.Now().Add(-time.Hour), // Set to 1 hour ago
	}

	oldActivity := client.LastActivity()

	// Update activity
	client.updateActivity()

	newActivity := client.LastActivity()

	if !newActivity.After(oldActivity) {
		t.Error("Expected last activity to be updated to a more recent time")
	}
}

func TestClient_HandleSubscription(t *testing.T) {
	client := &Client{
		id:            "test-client",
		subscriptions: make(map[SubscriptionType]bool),
		ctx:           context.Background(),
		connectedAt:   time.Now(),
		lastActivity:  time.Now(),
	}

	// Test valid subscription request
	subReq := SubscriptionRequest{
		Action:       "subscribe",
		Subscription: SubscriptionWhatsApp,
	}

	msg, err := NewMessage(MessageTypeSubscribe, subReq)
	if err != nil {
		t.Fatalf("Failed to create subscription message: %v", err)
	}

	client.handleSubscription(msg)

	if !client.IsSubscribed(SubscriptionWhatsApp) {
		t.Error("Expected client to be subscribed to WhatsApp")
	}

	// Test unsubscription
	unsubReq := SubscriptionRequest{
		Action:       "unsubscribe",
		Subscription: SubscriptionWhatsApp,
	}

	unsubMsg, err := NewMessage(MessageTypeUnsubscribe, unsubReq)
	if err != nil {
		t.Fatalf("Failed to create unsubscription message: %v", err)
	}

	client.handleSubscription(unsubMsg)

	if client.IsSubscribed(SubscriptionWhatsApp) {
		t.Error("Expected client not to be subscribed to WhatsApp after unsubscribing")
	}
}

func TestClient_HandleHeartbeat(t *testing.T) {
	client := &Client{
		id:           "test-client",
		connectedAt:  time.Now(),
		lastActivity: time.Now().Add(-time.Minute), // Set to 1 minute ago
	}

	oldActivity := client.LastActivity()

	heartbeat := HeartbeatMessage{
		ClientID:  "test-client",
		Timestamp: time.Now(),
	}

	msg, err := NewMessage(MessageTypeHeartbeat, heartbeat)
	if err != nil {
		t.Fatalf("Failed to create heartbeat message: %v", err)
	}

	client.handleHeartbeat(msg)

	newActivity := client.LastActivity()
	if !newActivity.After(oldActivity) {
		t.Error("Expected last activity to be updated after heartbeat")
	}
}

func TestClient_HandlePing(t *testing.T) {
	client := &Client{
		id:           "test-client",
		send:         make(chan []byte, 10),
		ctx:          context.Background(),
		connectedAt:  time.Now(),
		lastActivity: time.Now(),
	}

	pingMsg, err := NewMessage(MessageTypePing, nil)
	if err != nil {
		t.Fatalf("Failed to create ping message: %v", err)
	}

	client.handlePing(pingMsg)

	// Should have queued a pong response
	select {
	case data := <-client.send:
		msg, err := ParseMessage(data)
		if err != nil {
			t.Fatalf("Failed to parse pong message: %v", err)
		}
		if msg.Type != MessageTypePong {
			t.Errorf("Expected pong message, got %s", msg.Type)
		}
	case <-time.After(100 * time.Millisecond):
		t.Error("Expected pong response to be queued")
	}
}

func TestClient_Close(t *testing.T) {
	ctx, cancel := context.WithCancel(context.Background())

	client := &Client{
		id:     "test-client",
		send:   make(chan []byte, 10),
		ctx:    ctx,
		cancel: cancel,
	}

	// Close the client
	client.Close()

	// Context should be cancelled
	select {
	case <-client.ctx.Done():
		// Expected
	case <-time.After(100 * time.Millisecond):
		t.Error("Expected context to be cancelled")
	}

	// Send channel should be closed
	select {
	case _, ok := <-client.send:
		if ok {
			t.Error("Expected send channel to be closed")
		}
	case <-time.After(100 * time.Millisecond):
		t.Error("Expected send channel to be closed")
	}
}
