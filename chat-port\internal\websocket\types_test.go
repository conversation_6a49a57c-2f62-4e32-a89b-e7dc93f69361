package websocket

import (
	"encoding/json"
	"testing"
	"time"
)

func TestMessageType_Constants(t *testing.T) {
	// Test that all message type constants are defined
	expectedTypes := []MessageType{
		MessageTypeIncomingWhatsApp,
		MessageTypeStatusUpdate,
		MessageTypeMetricsUpdate,
		MessageTypeHealthUpdate,
		MessageTypeError,
		MessageTypePing,
		MessageTypePong,
		MessageTypeSubscribe,
		MessageTypeUnsubscribe,
		MessageTypeHeartbeat,
	}

	for _, msgType := range expectedTypes {
		if string(msgType) == "" {
			t.Errorf("Message type %v should not be empty", msgType)
		}
	}
}

func TestSubscriptionType_Constants(t *testing.T) {
	// Test that all subscription type constants are defined
	expectedTypes := []SubscriptionType{
		SubscriptionWhatsApp,
		SubscriptionStatus,
		SubscriptionMetrics,
		SubscriptionHealth,
		SubscriptionAll,
	}

	for _, subType := range expectedTypes {
		if string(subType) == "" {
			t.<PERSON><PERSON>rf("Subscription type %v should not be empty", subType)
		}
	}
}

func TestNewMessage(t *testing.T) {
	tests := []struct {
		name    string
		msgType MessageType
		data    interface{}
		wantErr bool
	}{
		{
			name:    "valid message with data",
			msgType: MessageTypeIncomingWhatsApp,
			data:    map[string]string{"test": "data"},
			wantErr: false,
		},
		{
			name:    "valid message without data",
			msgType: MessageTypePing,
			data:    nil,
			wantErr: false,
		},
		{
			name:    "message with unmarshalable data",
			msgType: MessageTypeError,
			data:    make(chan int), // channels can't be marshaled
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			msg, err := NewMessage(tt.msgType, tt.data)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if msg.Type != tt.msgType {
				t.Errorf("Expected type %s, got %s", tt.msgType, msg.Type)
			}

			if msg.Timestamp.IsZero() {
				t.Error("Expected timestamp to be set")
			}

			if tt.data != nil && msg.Data == nil {
				t.Error("Expected data to be set")
			}

			if tt.data == nil && msg.Data != nil {
				t.Error("Expected data to be nil")
			}
		})
	}
}

func TestNewErrorMessage(t *testing.T) {
	code := "TEST_ERROR"
	message := "Test error message"
	details := "Error details"

	msg, err := NewErrorMessage(code, message, details)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}

	if msg.Type != MessageTypeError {
		t.Errorf("Expected type %s, got %s", MessageTypeError, msg.Type)
	}

	var errorData ErrorMessage
	err = msg.GetData(&errorData)
	if err != nil {
		t.Fatalf("Failed to get error data: %v", err)
	}

	if errorData.Code != code {
		t.Errorf("Expected code %s, got %s", code, errorData.Code)
	}

	if errorData.Message != message {
		t.Errorf("Expected message %s, got %s", message, errorData.Message)
	}

	if errorData.Details != details {
		t.Errorf("Expected details %s, got %s", details, errorData.Details)
	}
}

func TestNewIncomingWhatsAppMessage(t *testing.T) {
	from := "<EMAIL>"
	message := "Hello, World!"
	messageID := "msg123"

	msg, err := NewIncomingWhatsAppMessage(from, message, messageID)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}

	if msg.Type != MessageTypeIncomingWhatsApp {
		t.Errorf("Expected type %s, got %s", MessageTypeIncomingWhatsApp, msg.Type)
	}

	var whatsappData IncomingWhatsAppMessage
	err = msg.GetData(&whatsappData)
	if err != nil {
		t.Fatalf("Failed to get WhatsApp data: %v", err)
	}

	if whatsappData.From != from {
		t.Errorf("Expected from %s, got %s", from, whatsappData.From)
	}

	if whatsappData.Message != message {
		t.Errorf("Expected message %s, got %s", message, whatsappData.Message)
	}

	if whatsappData.MessageID != messageID {
		t.Errorf("Expected messageID %s, got %s", messageID, whatsappData.MessageID)
	}

	if whatsappData.Timestamp.IsZero() {
		t.Error("Expected timestamp to be set")
	}
}

func TestNewStatusUpdate(t *testing.T) {
	service := "whatsapp"
	status := "connected"
	details := "Connection established"

	msg, err := NewStatusUpdate(service, status, details)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}

	if msg.Type != MessageTypeStatusUpdate {
		t.Errorf("Expected type %s, got %s", MessageTypeStatusUpdate, msg.Type)
	}

	var statusData StatusUpdate
	err = msg.GetData(&statusData)
	if err != nil {
		t.Fatalf("Failed to get status data: %v", err)
	}

	if statusData.Service != service {
		t.Errorf("Expected service %s, got %s", service, statusData.Service)
	}

	if statusData.Status != status {
		t.Errorf("Expected status %s, got %s", status, statusData.Status)
	}

	if statusData.Details != details {
		t.Errorf("Expected details %s, got %s", details, statusData.Details)
	}
}

func TestParseMessage(t *testing.T) {
	// Create a test message
	originalMsg := &Message{
		Type:      MessageTypePing,
		Timestamp: time.Now(),
		Data:      json.RawMessage(`{"test": "data"}`),
	}

	// Marshal to JSON
	data, err := originalMsg.ToJSON()
	if err != nil {
		t.Fatalf("Failed to marshal message: %v", err)
	}

	// Parse back
	parsedMsg, err := ParseMessage(data)
	if err != nil {
		t.Fatalf("Failed to parse message: %v", err)
	}

	if parsedMsg.Type != originalMsg.Type {
		t.Errorf("Expected type %s, got %s", originalMsg.Type, parsedMsg.Type)
	}

	// Test invalid JSON
	_, err = ParseMessage([]byte("invalid json"))
	if err == nil {
		t.Error("Expected error for invalid JSON")
	}
}

func TestMessage_IsValid(t *testing.T) {
	tests := []struct {
		name    string
		message *Message
		valid   bool
	}{
		{
			name: "valid message",
			message: &Message{
				Type:      MessageTypePing,
				Timestamp: time.Now(),
			},
			valid: true,
		},
		{
			name: "missing type",
			message: &Message{
				Timestamp: time.Now(),
			},
			valid: false,
		},
		{
			name: "missing timestamp",
			message: &Message{
				Type: MessageTypePing,
			},
			valid: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.message.IsValid() != tt.valid {
				t.Errorf("Expected IsValid() to return %v", tt.valid)
			}
		})
	}
}

func TestMessage_IsSubscriptionMessage(t *testing.T) {
	tests := []struct {
		msgType  MessageType
		isSubMsg bool
	}{
		{MessageTypeSubscribe, true},
		{MessageTypeUnsubscribe, true},
		{MessageTypePing, false},
		{MessageTypeIncomingWhatsApp, false},
	}

	for _, tt := range tests {
		t.Run(string(tt.msgType), func(t *testing.T) {
			msg := &Message{Type: tt.msgType}
			if msg.IsSubscriptionMessage() != tt.isSubMsg {
				t.Errorf("Expected IsSubscriptionMessage() to return %v for %s", tt.isSubMsg, tt.msgType)
			}
		})
	}
}

func TestMessage_IsHeartbeatMessage(t *testing.T) {
	tests := []struct {
		msgType     MessageType
		isHeartbeat bool
	}{
		{MessageTypeHeartbeat, true},
		{MessageTypePing, true},
		{MessageTypePong, true},
		{MessageTypeIncomingWhatsApp, false},
		{MessageTypeError, false},
	}

	for _, tt := range tests {
		t.Run(string(tt.msgType), func(t *testing.T) {
			msg := &Message{Type: tt.msgType}
			if msg.IsHeartbeatMessage() != tt.isHeartbeat {
				t.Errorf("Expected IsHeartbeatMessage() to return %v for %s", tt.isHeartbeat, tt.msgType)
			}
		})
	}
}

func TestIsValidSubscriptionType(t *testing.T) {
	validTypes := ValidSubscriptionTypes()

	// Test all valid types
	for _, validType := range validTypes {
		if !IsValidSubscriptionType(validType) {
			t.Errorf("Expected %s to be valid", validType)
		}
	}

	// Test invalid type
	if IsValidSubscriptionType("invalid") {
		t.Error("Expected 'invalid' to be invalid subscription type")
	}
}

func TestMessage_GetData(t *testing.T) {
	// Test with data
	testData := map[string]string{"key": "value"}
	jsonData, _ := json.Marshal(testData)

	msg := &Message{
		Type:      MessageTypeIncomingWhatsApp,
		Timestamp: time.Now(),
		Data:      jsonData,
	}

	var result map[string]string
	err := msg.GetData(&result)
	if err != nil {
		t.Fatalf("Failed to get data: %v", err)
	}

	if result["key"] != "value" {
		t.Errorf("Expected key=value, got key=%s", result["key"])
	}

	// Test with nil data
	msgNil := &Message{
		Type:      MessageTypePing,
		Timestamp: time.Now(),
		Data:      nil,
	}

	var nilResult map[string]string
	err = msgNil.GetData(&nilResult)
	if err != nil {
		t.Errorf("Expected no error for nil data, got: %v", err)
	}
}

func TestMessage_ToJSON(t *testing.T) {
	msg := &Message{
		Type:      MessageTypePing,
		Timestamp: time.Now(),
		Data:      json.RawMessage(`{"test": "data"}`),
	}

	data, err := msg.ToJSON()
	if err != nil {
		t.Fatalf("Failed to convert to JSON: %v", err)
	}

	// Verify it's valid JSON
	var result map[string]interface{}
	err = json.Unmarshal(data, &result)
	if err != nil {
		t.Fatalf("Result is not valid JSON: %v", err)
	}

	if result["type"] != string(MessageTypePing) {
		t.Errorf("Expected type %s in JSON", MessageTypePing)
	}
}
