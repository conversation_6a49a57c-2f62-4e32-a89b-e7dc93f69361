package logger

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"chatport-go/config"
)

// Logger levels
const (
	LevelDebug = "debug"
	LevelInfo  = "info"
	LevelWarn  = "warn"
	LevelError = "error"
)

// Logger represents a structured logger
type Logger struct {
	level  string
	format string
	prefix string
}

var globalLogger *Logger

// Init initializes the global logger with configuration
func Init(cfg *config.Config) {
	globalLogger = &Logger{
		level:  strings.ToLower(cfg.Logging.Level),
		format: strings.ToLower(cfg.Logging.Format),
		prefix: "ChatPort",
	}

	// Set standard log flags
	log.SetFlags(log.LstdFlags | log.Lmicroseconds)
}

// Get returns the global logger instance
func Get() *Logger {
	if globalLogger == nil {
		// Fallback to default logger if not initialized
		globalLogger = &Logger{
			level:  LevelInfo,
			format: "text",
			prefix: "ChatPort",
		}
	}
	return globalLogger
}

// Debug logs a debug message
func (l *Logger) Debug(msg string) {
	if l.shouldLog(LevelDebug) {
		l.log(LevelDebug, msg, nil)
	}
}

// Debugf logs a formatted debug message
func (l *Logger) Debugf(format string, args ...interface{}) {
	if l.shouldLog(LevelDebug) {
		l.log(LevelDebug, fmt.Sprintf(format, args...), nil)
	}
}

// Info logs an info message
func (l *Logger) Info(msg string) {
	if l.shouldLog(LevelInfo) {
		l.log(LevelInfo, msg, nil)
	}
}

// Infof logs a formatted info message
func (l *Logger) Infof(format string, args ...interface{}) {
	if l.shouldLog(LevelInfo) {
		l.log(LevelInfo, fmt.Sprintf(format, args...), nil)
	}
}

// Warn logs a warning message
func (l *Logger) Warn(msg string) {
	if l.shouldLog(LevelWarn) {
		l.log(LevelWarn, msg, nil)
	}
}

// Warnf logs a formatted warning message
func (l *Logger) Warnf(format string, args ...interface{}) {
	if l.shouldLog(LevelWarn) {
		l.log(LevelWarn, fmt.Sprintf(format, args...), nil)
	}
}

// Error logs an error message
func (l *Logger) Error(msg string) {
	if l.shouldLog(LevelError) {
		l.log(LevelError, msg, nil)
	}
}

// Errorf logs a formatted error message
func (l *Logger) Errorf(format string, args ...interface{}) {
	if l.shouldLog(LevelError) {
		l.log(LevelError, fmt.Sprintf(format, args...), nil)
	}
}

// ErrorWithFields logs an error message with additional fields
func (l *Logger) ErrorWithFields(msg string, fields map[string]interface{}) {
	if l.shouldLog(LevelError) {
		l.log(LevelError, msg, fields)
	}
}

// Fatal logs a fatal message and exits
func (l *Logger) Fatal(msg string) {
	l.log(LevelError, msg, nil)
	os.Exit(1)
}

// Fatalf logs a formatted fatal message and exits
func (l *Logger) Fatalf(format string, args ...interface{}) {
	l.log(LevelError, fmt.Sprintf(format, args...), nil)
	os.Exit(1)
}

// shouldLog determines if a message should be logged based on level
func (l *Logger) shouldLog(level string) bool {
	levels := map[string]int{
		LevelDebug: 0,
		LevelInfo:  1,
		LevelWarn:  2,
		LevelError: 3,
	}

	currentLevel, exists := levels[l.level]
	if !exists {
		currentLevel = levels[LevelInfo]
	}

	messageLevel, exists := levels[level]
	if !exists {
		messageLevel = levels[LevelInfo]
	}

	return messageLevel >= currentLevel
}

// log performs the actual logging
func (l *Logger) log(level, msg string, fields map[string]interface{}) {
	timestamp := time.Now().Format("2006-01-02 15:04:05.000")

	if l.format == "json" {
		l.logJSON(timestamp, level, msg, fields)
	} else {
		l.logText(timestamp, level, msg, fields)
	}
}

// logText logs in text format
func (l *Logger) logText(timestamp, level, msg string, fields map[string]interface{}) {
	levelUpper := strings.ToUpper(level)
	logMsg := fmt.Sprintf("%s [%s] %s: %s", timestamp, levelUpper, l.prefix, msg)

	if len(fields) > 0 {
		logMsg += " |"
		for key, value := range fields {
			logMsg += fmt.Sprintf(" %s=%v", key, value)
		}
	}

	log.Println(logMsg)
}

// logJSON logs in JSON format
func (l *Logger) logJSON(timestamp, level, msg string, fields map[string]interface{}) {
	jsonMsg := fmt.Sprintf(`{"timestamp":"%s","level":"%s","service":"%s","message":"%s"`,
		timestamp, level, l.prefix, msg)

	if len(fields) > 0 {
		for key, value := range fields {
			jsonMsg += fmt.Sprintf(`,"%s":"%v"`, key, value)
		}
	}

	jsonMsg += "}"
	log.Println(jsonMsg)
}

// Global convenience functions
func Debug(msg string) {
	Get().Debug(msg)
}

func Debugf(format string, args ...interface{}) {
	Get().Debugf(format, args...)
}

func Info(msg string) {
	Get().Info(msg)
}

func Infof(format string, args ...interface{}) {
	Get().Infof(format, args...)
}

func Warn(msg string) {
	Get().Warn(msg)
}

func Warnf(format string, args ...interface{}) {
	Get().Warnf(format, args...)
}

func Error(msg string) {
	Get().Error(msg)
}

func Errorf(format string, args ...interface{}) {
	Get().Errorf(format, args...)
}

func ErrorWithFields(msg string, fields map[string]interface{}) {
	Get().ErrorWithFields(msg, fields)
}

func Fatal(msg string) {
	Get().Fatal(msg)
}

func Fatalf(format string, args ...interface{}) {
	Get().Fatalf(format, args...)
}
