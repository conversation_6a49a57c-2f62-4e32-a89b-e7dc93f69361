# ChatPort WhatsApp Service Configuration

# =============================================================================
# Server Configuration
# =============================================================================

# HTTP server port
SERVER_PORT=8081

# Server timeouts (Go duration format: 1s, 1m, 1h)
SERVER_READ_TIMEOUT=15s
SERVER_WRITE_TIMEOUT=15s
SERVER_IDLE_TIMEOUT=60s

# =============================================================================
# WhatsApp Configuration
# =============================================================================

# WhatsApp session database path
WHATSAPP_DB_PATH=file:session.db?_foreign_keys=on

# QR code scan timeout
WHATSAPP_QR_TIMEOUT=2m

# Reconnection settings
WHATSAPP_RECONNECT_DELAY=5s
WHATSAPP_MAX_RECONNECTS=5

# =============================================================================
# WebSocket Configuration
# =============================================================================

# Enable WebSocket server
WEBSOCKET_ENABLED=true

# WebSocket server settings
WEBSOCKET_MAX_CLIENTS=100
WEBSOCKET_CLEANUP_INTERVAL=30s
WEBSOCKET_CLIENT_TIMEOUT=5m
WEBSOCKET_BROADCAST_BUFFER_SIZE=100

# WebSocket broadcasting settings
WEBSOCKET_ENABLE_METRICS_BROADCAST=true
WEBSOCKET_ENABLE_HEALTH_BROADCAST=true
WEBSOCKET_METRICS_INTERVAL=10s

# =============================================================================
# Logging Configuration
# =============================================================================

# Log level: debug, info, warn, error
LOG_LEVEL=info

# Log format: text, json
LOG_FORMAT=text
