package config

import (
	"fmt"
	"log"
	"os"
	"slices"
	"strconv"
	"strings"
	"time"

	"github.com/joho/godotenv"
)

// Config holds all configuration values
type Config struct {
	// Server configuration
	Server ServerConfig `json:"server"`

	// WhatsApp configuration
	WhatsApp WhatsAppConfig `json:"whatsapp"`

	// Logging configuration
	Logging LoggingConfig `json:"logging"`

	// WebSocket configuration
	WebSocket WebSocketConfig `json:"websocket"`
}

// ServerConfig holds HTTP server configuration
type ServerConfig struct {
	Port         string        `json:"port"`
	ReadTimeout  time.Duration `json:"read_timeout"`
	WriteTimeout time.Duration `json:"write_timeout"`
	IdleTimeout  time.Duration `json:"idle_timeout"`
}

// WhatsAppConfig holds WhatsApp client configuration
type WhatsAppConfig struct {
	DBPath         string        `json:"db_path"`
	QRTimeout      time.Duration `json:"qr_timeout"`
	ReconnectDelay time.Duration `json:"reconnect_delay"`
	MaxReconnects  int           `json:"max_reconnects"`
}

// LoggingConfig holds logging configuration
type LoggingConfig struct {
	Level  string `json:"level"`
	Format string `json:"format"`
}

// WebSocketConfig holds WebSocket configuration
type WebSocketConfig struct {
	Enabled             bool          `json:"enabled"`
	MaxClients          int           `json:"max_clients"`
	ReadBufferSize      int           `json:"read_buffer_size"`
	WriteBufferSize     int           `json:"write_buffer_size"`
	EnableCompression   bool          `json:"enable_compression"`
	ClientTimeout       time.Duration `json:"client_timeout"`
	CleanupInterval     time.Duration `json:"cleanup_interval"`
	MetricsInterval     time.Duration `json:"metrics_interval"`
	BroadcastBufferSize int           `json:"broadcast_buffer_size"`
	AllowedOrigins      []string      `json:"allowed_origins"`
	EnableAuth          bool          `json:"enable_auth"`
	RateLimit           int           `json:"rate_limit"`
	RateLimitWindow     time.Duration `json:"rate_limit_window"`
}

var globalConfig *Config

// LoadEnv loads environment variables from .env file
func LoadEnv() {
	err := godotenv.Load()
	if err != nil {
		log.Println("No .env file found, using system environment variables")
	}
}

// Load loads and validates the configuration
func Load() (*Config, error) {
	LoadEnv()

	config := &Config{
		Server: ServerConfig{
			Port:         GetEnv("SERVER_PORT", "8081"),
			ReadTimeout:  GetDurationEnv("SERVER_READ_TIMEOUT", 15*time.Second),
			WriteTimeout: GetDurationEnv("SERVER_WRITE_TIMEOUT", 15*time.Second),
			IdleTimeout:  GetDurationEnv("SERVER_IDLE_TIMEOUT", 60*time.Second),
		},
		WhatsApp: WhatsAppConfig{
			DBPath:         GetEnv("WHATSAPP_DB_PATH", "file:session.db?_foreign_keys=on"),
			QRTimeout:      GetDurationEnv("WHATSAPP_QR_TIMEOUT", 2*time.Minute),
			ReconnectDelay: GetDurationEnv("WHATSAPP_RECONNECT_DELAY", 5*time.Second),
			MaxReconnects:  GetIntEnv("WHATSAPP_MAX_RECONNECTS", 5),
		},
		Logging: LoggingConfig{
			Level:  GetEnv("LOG_LEVEL", "info"),
			Format: GetEnv("LOG_FORMAT", "text"),
		},
		WebSocket: WebSocketConfig{
			Enabled:             GetBoolEnv("WEBSOCKET_ENABLED", true),
			MaxClients:          GetIntEnv("WEBSOCKET_MAX_CLIENTS", 1000),
			ReadBufferSize:      GetIntEnv("WEBSOCKET_READ_BUFFER_SIZE", 1024),
			WriteBufferSize:     GetIntEnv("WEBSOCKET_WRITE_BUFFER_SIZE", 1024),
			EnableCompression:   GetBoolEnv("WEBSOCKET_ENABLE_COMPRESSION", true),
			ClientTimeout:       GetDurationEnv("WEBSOCKET_CLIENT_TIMEOUT", 5*time.Minute),
			CleanupInterval:     GetDurationEnv("WEBSOCKET_CLEANUP_INTERVAL", 30*time.Second),
			MetricsInterval:     GetDurationEnv("WEBSOCKET_METRICS_INTERVAL", 10*time.Second),
			BroadcastBufferSize: GetIntEnv("WEBSOCKET_BROADCAST_BUFFER_SIZE", 1000),
			AllowedOrigins:      []string{"*"}, // Default to allow all origins
			EnableAuth:          GetBoolEnv("WEBSOCKET_ENABLE_AUTH", false),
			RateLimit:           GetIntEnv("WEBSOCKET_RATE_LIMIT", 10),
			RateLimitWindow:     GetDurationEnv("WEBSOCKET_RATE_LIMIT_WINDOW", 1*time.Minute),
		},
	}

	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	globalConfig = config
	return config, nil
}

// Get returns the global configuration instance
func Get() *Config {
	if globalConfig == nil {
		config, err := Load()
		if err != nil {
			log.Fatalf("Failed to load configuration: %v", err)
		}
		return config
	}
	return globalConfig
}

// GetEnv gets an environment variable with a fallback value
func GetEnv(key string, fallback string) string {
	if value, ok := os.LookupEnv(key); ok {
		return value
	}
	return fallback
}

// GetIntEnv gets an integer environment variable with a fallback value
func GetIntEnv(key string, fallback int) int {
	if value, ok := os.LookupEnv(key); ok {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
		log.Printf("Warning: Invalid integer value for %s: %s, using fallback: %d", key, value, fallback)
	}
	return fallback
}

// GetDurationEnv gets a duration environment variable with a fallback value
func GetDurationEnv(key string, fallback time.Duration) time.Duration {
	if value, ok := os.LookupEnv(key); ok {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
		log.Printf("Warning: Invalid duration value for %s: %s, using fallback: %v", key, value, fallback)
	}
	return fallback
}

// GetBoolEnv gets a boolean environment variable with a fallback value
func GetBoolEnv(key string, fallback bool) bool {
	if value, ok := os.LookupEnv(key); ok {
		switch strings.ToLower(value) {
		case "true", "1", "yes", "on":
			return true
		case "false", "0", "no", "off":
			return false
		default:
			log.Printf("Warning: Invalid boolean value for %s: %s, using fallback: %v", key, value, fallback)
		}
	}
	return fallback
}

// Validate validates the configuration values
func (c *Config) Validate() error {
	// Validate server configuration
	if c.Server.Port == "" {
		return fmt.Errorf("server port cannot be empty")
	}

	if c.Server.ReadTimeout <= 0 {
		return fmt.Errorf("server read timeout must be positive")
	}

	if c.Server.WriteTimeout <= 0 {
		return fmt.Errorf("server write timeout must be positive")
	}

	if c.Server.IdleTimeout <= 0 {
		return fmt.Errorf("server idle timeout must be positive")
	}

	// Validate WhatsApp configuration
	if c.WhatsApp.DBPath == "" {
		return fmt.Errorf("WhatsApp database path cannot be empty")
	}

	if c.WhatsApp.QRTimeout <= 0 {
		return fmt.Errorf("WhatsApp QR timeout must be positive")
	}

	if c.WhatsApp.ReconnectDelay <= 0 {
		return fmt.Errorf("WhatsApp reconnect delay must be positive")
	}

	if c.WhatsApp.MaxReconnects < 0 {
		return fmt.Errorf("WhatsApp max reconnects cannot be negative")
	}

	// Validate logging configuration
	validLogLevels := []string{"debug", "info", "warn", "error"}
	validLevel := slices.Contains(validLogLevels, strings.ToLower(c.Logging.Level))
	if !validLevel {
		return fmt.Errorf("invalid log level: %s, must be one of: %v", c.Logging.Level, validLogLevels)
	}

	validLogFormats := []string{"text", "json"}
	validFormat := slices.Contains(validLogFormats, strings.ToLower(c.Logging.Format))
	if !validFormat {
		return fmt.Errorf("invalid log format: %s, must be one of: %v", c.Logging.Format, validLogFormats)
	}

	// Validate WebSocket configuration
	if c.WebSocket.MaxClients <= 0 {
		return fmt.Errorf("WebSocket max clients must be positive")
	}

	if c.WebSocket.ReadBufferSize <= 0 {
		return fmt.Errorf("WebSocket read buffer size must be positive")
	}

	if c.WebSocket.WriteBufferSize <= 0 {
		return fmt.Errorf("WebSocket write buffer size must be positive")
	}

	if c.WebSocket.ClientTimeout <= 0 {
		return fmt.Errorf("WebSocket client timeout must be positive")
	}

	if c.WebSocket.CleanupInterval <= 0 {
		return fmt.Errorf("WebSocket cleanup interval must be positive")
	}

	if c.WebSocket.MetricsInterval <= 0 {
		return fmt.Errorf("WebSocket metrics interval must be positive")
	}

	if c.WebSocket.BroadcastBufferSize <= 0 {
		return fmt.Errorf("WebSocket broadcast buffer size must be positive")
	}

	if c.WebSocket.RateLimit <= 0 {
		return fmt.Errorf("WebSocket rate limit must be positive")
	}

	if c.WebSocket.RateLimitWindow <= 0 {
		return fmt.Errorf("WebSocket rate limit window must be positive")
	}

	return nil
}
