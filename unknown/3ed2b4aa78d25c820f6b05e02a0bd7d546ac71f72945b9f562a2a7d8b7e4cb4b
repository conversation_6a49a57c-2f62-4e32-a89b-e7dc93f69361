package handlers

import (
	"encoding/json"
	"net/http"
	"time"

	"chatport-go/internal/client"
)

// HealthResponse represents the health check response
type HealthResponse struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version"`
	Services  Services  `json:"services"`
}

// Services represents the status of various services
type Services struct {
	WhatsApp WhatsAppStatus `json:"whatsapp"`
}

// WhatsAppStatus represents WhatsApp connection status
type WhatsAppStatus struct {
	Connected bool   `json:"connected"`
	JID       string `json:"jid,omitempty"`
}

// HealthHandler handles GET /api/health requests
func HealthHandler(w http.ResponseWriter, r *http.Request) {
	var whatsappStatus WhatsAppStatus

	if client.Client != nil {
		whatsappStatus.Connected = client.Client.IsConnected()
		whatsappStatus.JID = client.Client.GetJID()
	}

	response := HealthResponse{
		Status:    "ok",
		Timestamp: time.Now(),
		Version:   "1.0.0",
		Services: Services{
			WhatsApp: whatsappStatus,
		},
	}

	// If WhatsApp is not connected, mark as degraded
	if !whatsappStatus.Connected {
		response.Status = "degraded"
	}

	statusCode := http.StatusOK
	if response.Status == "degraded" {
		statusCode = http.StatusServiceUnavailable
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)
	json.NewEncoder(w).Encode(response)
}
