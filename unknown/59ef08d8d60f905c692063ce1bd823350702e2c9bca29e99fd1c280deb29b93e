package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"runtime"
	"slices"
	"strings"
	"testing"
	"time"

	"chatport-go/internal/client"
)

func TestStatusHandler(t *testing.T) {
	tests := []struct {
		name           string
		clientSetup    func()
		expectedStatus int
	}{
		{
			name: "status with client not initialized",
			clientSetup: func() {
				client.Client = nil
			},
			expectedStatus: http.StatusOK,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			tt.clientSetup()

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/api/status", nil)
			rr := httptest.NewRecorder()

			// Call handler
			StatusHandler(rr, req)

			// Check status code
			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			// Check content type
			contentType := rr.Header().Get("Content-Type")
			if contentType != "application/json" {
				t.Errorf("Expected Content-Type application/json, got %s", contentType)
			}

			// Parse response
			var response StatusResponse
			err := json.NewDecoder(rr.Body).Decode(&response)
			if err != nil {
				t.Fatalf("Failed to decode response: %v", err)
			}

			// Check required fields
			if response.Application.Name == "" {
				t.Error("Expected application name to be present")
			}

			if response.Application.Version == "" {
				t.Error("Expected application version to be present")
			}

			if response.Application.Uptime == "" {
				t.Error("Expected application uptime to be present")
			}

			if response.System.GoVersion == "" {
				t.Error("Expected Go version to be present")
			}

			if response.System.NumCPU <= 0 {
				t.Error("Expected positive number of CPUs")
			}

			if response.System.NumGoroutine <= 0 {
				t.Error("Expected positive number of goroutines")
			}

			if response.Timestamp.IsZero() {
				t.Error("Expected timestamp to be present")
			}

			// Check timestamp is recent (within last minute)
			if time.Since(response.Timestamp) > time.Minute {
				t.Error("Expected timestamp to be recent")
			}
		})
	}
}

func TestStatusHandler_ApplicationInfo(t *testing.T) {
	req := httptest.NewRequest(http.MethodGet, "/api/status", nil)
	rr := httptest.NewRecorder()

	StatusHandler(rr, req)

	var response StatusResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Check application info
	expectedName := "ChatPort WhatsApp Service"
	if response.Application.Name != expectedName {
		t.Errorf("Expected application name %q, got %q", expectedName, response.Application.Name)
	}

	expectedVersion := "1.0.0"
	if response.Application.Version != expectedVersion {
		t.Errorf("Expected version %q, got %q", expectedVersion, response.Application.Version)
	}

	// Uptime should be parseable as duration
	_, err = time.ParseDuration(response.Application.Uptime)
	if err != nil {
		t.Errorf("Expected uptime to be valid duration, got %q: %v", response.Application.Uptime, err)
	}
}

func TestStatusHandler_SystemInfo(t *testing.T) {
	req := httptest.NewRequest(http.MethodGet, "/api/status", nil)
	rr := httptest.NewRecorder()

	StatusHandler(rr, req)

	var response StatusResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Check system info
	expectedGoVersion := runtime.Version()
	if response.System.GoVersion != expectedGoVersion {
		t.Errorf("Expected Go version %q, got %q", expectedGoVersion, response.System.GoVersion)
	}

	expectedNumCPU := runtime.NumCPU()
	if response.System.NumCPU != expectedNumCPU {
		t.Errorf("Expected %d CPUs, got %d", expectedNumCPU, response.System.NumCPU)
	}

	// NumGoroutine should be reasonable (at least 1, but not too many for a test)
	if response.System.NumGoroutine < 1 || response.System.NumGoroutine > 100 {
		t.Errorf("Expected reasonable number of goroutines, got %d", response.System.NumGoroutine)
	}
}

func TestStatusHandler_WhatsAppInfo(t *testing.T) {
	tests := []struct {
		name            string
		clientSetup     func()
		expectedStatus  string
		expectedJID     string
		expectedConnect bool
	}{
		{
			name: "client not initialized",
			clientSetup: func() {
				client.Client = nil
			},
			expectedStatus:  "not_initialized",
			expectedJID:     "",
			expectedConnect: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			tt.clientSetup()

			req := httptest.NewRequest(http.MethodGet, "/api/status", nil)
			rr := httptest.NewRecorder()

			StatusHandler(rr, req)

			var response StatusResponse
			err := json.NewDecoder(rr.Body).Decode(&response)
			if err != nil {
				t.Fatalf("Failed to decode response: %v", err)
			}

			// Check WhatsApp info
			if response.WhatsApp.Status != tt.expectedStatus {
				t.Errorf("Expected WhatsApp status %q, got %q", tt.expectedStatus, response.WhatsApp.Status)
			}

			if response.WhatsApp.JID != tt.expectedJID {
				t.Errorf("Expected WhatsApp JID %q, got %q", tt.expectedJID, response.WhatsApp.JID)
			}

			if response.WhatsApp.Connected != tt.expectedConnect {
				t.Errorf("Expected WhatsApp connected %v, got %v", tt.expectedConnect, response.WhatsApp.Connected)
			}
		})
	}
}

func TestStatusHandler_JSONStructure(t *testing.T) {
	req := httptest.NewRequest(http.MethodGet, "/api/status", nil)
	rr := httptest.NewRecorder()

	StatusHandler(rr, req)

	// Verify response is valid JSON
	var result map[string]interface{}
	err := json.NewDecoder(rr.Body).Decode(&result)
	if err != nil {
		t.Fatalf("Response is not valid JSON: %v", err)
	}

	// Check expected top-level keys
	expectedKeys := []string{"application", "system", "whatsapp", "timestamp"}
	for _, key := range expectedKeys {
		if _, exists := result[key]; !exists {
			t.Errorf("Expected key %s not found in response", key)
		}
	}

	// Check nested structure
	if app, ok := result["application"].(map[string]interface{}); ok {
		appKeys := []string{"name", "version", "uptime"}
		for _, key := range appKeys {
			if _, exists := app[key]; !exists {
				t.Errorf("Expected application key %s not found", key)
			}
		}
	} else {
		t.Error("Expected application to be an object")
	}

	if sys, ok := result["system"].(map[string]interface{}); ok {
		sysKeys := []string{"go_version", "num_goroutine", "num_cpu"}
		for _, key := range sysKeys {
			if _, exists := sys[key]; !exists {
				t.Errorf("Expected system key %s not found", key)
			}
		}
	} else {
		t.Error("Expected system to be an object")
	}
}

func TestStatusHandler_HTTPMethod(t *testing.T) {
	// Test that handler works with GET method
	req := httptest.NewRequest(http.MethodGet, "/api/status", nil)
	rr := httptest.NewRecorder()

	StatusHandler(rr, req)

	if rr.Code == http.StatusMethodNotAllowed {
		t.Error("Handler should accept GET method")
	}

	if rr.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", rr.Code)
	}
}

func TestStatusHandler_NoBody(t *testing.T) {
	// Test that handler works without request body
	req := httptest.NewRequest(http.MethodGet, "/api/status", nil)
	rr := httptest.NewRecorder()

	StatusHandler(rr, req)

	// Should not fail
	if rr.Code >= 500 {
		t.Errorf("Handler should not fail with 5xx error, got status %d", rr.Code)
	}

	// Should return valid JSON
	var response StatusResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Errorf("Response should be valid JSON: %v", err)
	}
}

func TestStatusHandler_Uptime(t *testing.T) {
	// Test uptime calculation
	req := httptest.NewRequest(http.MethodGet, "/api/status", nil)
	rr := httptest.NewRecorder()

	StatusHandler(rr, req)

	var response StatusResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Parse uptime
	uptime, err := time.ParseDuration(response.Application.Uptime)
	if err != nil {
		t.Fatalf("Failed to parse uptime %q: %v", response.Application.Uptime, err)
	}

	// Uptime should be positive and reasonable (less than a day for tests)
	if uptime < 0 {
		t.Error("Uptime should be positive")
	}

	if uptime > 24*time.Hour {
		t.Error("Uptime seems unreasonably long for a test")
	}
}

func TestStatusResponse_Structure(t *testing.T) {
	// Test that the status response has the expected structure
	req := httptest.NewRequest(http.MethodGet, "/api/status", nil)
	rr := httptest.NewRecorder()

	StatusHandler(rr, req)

	var response StatusResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Check all required fields are present and have reasonable values
	if response.Application.Name == "" {
		t.Error("Application name is missing")
	}

	if response.Application.Version == "" {
		t.Error("Application version is missing")
	}

	if response.System.GoVersion == "" {
		t.Error("Go version is missing")
	}

	if !strings.HasPrefix(response.System.GoVersion, "go") {
		t.Errorf("Go version should start with 'go', got %q", response.System.GoVersion)
	}

	if response.WhatsApp.Status == "" {
		t.Error("WhatsApp status is missing")
	}

	validStatuses := []string{"connected", "disconnected", "not_initialized"}
	validStatus := slices.Contains(validStatuses, response.WhatsApp.Status)
	if !validStatus {
		t.Errorf("WhatsApp status should be one of %v, got %q", validStatuses, response.WhatsApp.Status)
	}
}
