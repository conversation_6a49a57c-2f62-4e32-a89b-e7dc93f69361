from fastapi import Depends, Head<PERSON>, HTTPException, status
from typing import Annotated

from config.settings import Settings, get_gemini_api_key, get_settings
from services.ai_service import AIService
from schemas.config import AIConfig


def get_ai_service() -> AIService:
    """Dependency that provides configured AI service"""
    settings = get_settings()
    ai_config = AIConfig(
        model=settings.gemini_model,
        temperature=settings.gemini_temperature,
        max_tokens=settings.gemini_max_tokens,
    )
    return AIService(api_key=get_gemini_api_key(), config=ai_config)


def verify_api_key(
    api_key: Annotated[
        str, Header(alias="X-API-Key", description="API Key for authentication")
    ],
    settings: Annotated[Settings, Depends(get_settings)],
) -> None:
    """Dependency for API key authentication"""
    if api_key != settings.service_api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid API Key"
        )
