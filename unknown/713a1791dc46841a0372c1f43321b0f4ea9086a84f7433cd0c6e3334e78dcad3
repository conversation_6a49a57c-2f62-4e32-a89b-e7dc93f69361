"""
Configuration management for Wellbot AI Service
"""

from functools import lru_cache
from typing import Optional
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from .exceptions import GeminiAPIKeyError


class Settings(BaseSettings):
    """Application settings with environment variable support"""

    model_config = SettingsConfigDict(
        env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore"
    )

    # Application settings
    app_name: str = Field(default="Wellbot AI Service", description="Application name")
    version: str = Field(default="1.0.0", description="Application version")
    debug: bool = Field(default=False, description="Debug mode")
    host: str = Field(default="0.0.0.0", description="Host to bind to")
    port: int = Field(default=8000, description="Port to bind to")

    # Database settings
    database_url: str = Field(
        default="postgresql+asyncpg://postgres:password@localhost:5432/wellbot",
        description="PostgreSQL database URL",
    )
    database_pool_size: int = Field(
        default=10, description="Database connection pool size"
    )
    database_max_overflow: int = Field(
        default=20, description="Database max overflow connections"
    )

    # Service API Key for client authentication
    service_api_key: str = Field(
        default="wellbot-dev-key-2025", description="API key for service authentication"
    )

    # AI/Gemini settings
    gemini_api_key: Optional[str] = Field(
        default=None, description="Gemini API key (optional)"
    )
    gemini_model: str = Field(
        default="gemini-2.5-flash", description="Gemini model to use"
    )
    gemini_temperature: float = Field(
        default=0.7, description="Gemini temperature setting"
    )
    gemini_max_tokens: int = Field(
        default=4000, description="Maximum tokens for Gemini response"
    )

    # RAG settings
    embedding_dimension: int = Field(
        default=768, description="Embedding vector dimension"
    )
    max_search_results: int = Field(
        default=5, description="Maximum search results for RAG"
    )
    similarity_threshold: float = Field(
        default=0.7, description="Similarity threshold for relevant results"
    )

    # Logging settings
    log_level: str = Field(default="INFO", description="Logging level")
    log_format: str = Field(default="json", description="Log format (json or text)")

    # CORS settings
    cors_origins: list[str] = Field(default=["*"], description="Allowed CORS origins")

    # Rate limiting
    rate_limit_requests: int = Field(
        default=100, description="Rate limit requests per minute"
    )
    rate_limit_window: int = Field(
        default=60, description="Rate limit window in seconds"
    )

    # Health check settings
    health_check_timeout: float = Field(
        default=5.0, description="Health check timeout in seconds"
    )

    # System prompt for AI
    system_prompt: str = Field(
        default="""You are a helpful pharmacy assistant AI. You help customers find medications, 
        provide information about drugs, suggest alternatives, and answer health-related questions.
        Always be professional, accurate, and helpful. If you're not sure about something, 
        recommend consulting with a pharmacist or doctor.""",
        description="System prompt for AI responses",
    )

    @field_validator("cors_origins", mode="before")
    @classmethod
    def parse_cors_origins(cls, v):
        if isinstance(v, str):
            if not v.strip():
                return []
            return [item.strip() for item in v.split(",")]
        return v

    @field_validator("gemini_api_key")
    @classmethod
    def validate_api_key(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError(
                "Invalid Gemini API Key configuration detected.\n"
                "You have provided an empty string for GEMINI_API_KEY.\n"
                "Please either:\n"
                "1. Provide a valid API key\n"
                "2. Remove the GEMINI_API_KEY entirely if not using Gemini\n"
                "3. Set it to 'None' to explicitly disable"
            )
        return v


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()


def get_database_url() -> str:
    """Get database URL from settings"""
    return get_settings().database_url


def get_gemini_api_key() -> str:
    """
    Get Gemini API key from settings with clear error messaging

    Returns:
        str: The validated Gemini API key

    Raises:
        GeminiAPIKeyError: With detailed configuration guidance
    """
    settings = get_settings()
    key = settings.gemini_api_key

    if key is None:
        raise GeminiAPIKeyError(
            "Gemini API features are disabled because no API key is configured.\n"
            "To enable Gemini features:\n"
            "1. Obtain an API key from Google AI Studio\n"
            "2. Add it to your .env file: GEMINI_API_KEY=your_key_here\n"
            "3. Restart the application"
        )

    return key
