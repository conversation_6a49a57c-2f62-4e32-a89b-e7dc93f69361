package config

import (
	"io"
	"log"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"
)

// tempEnv is a helper to temporarily set environment variables and reset them afterward.
func tempEnv(t *testing.T, envs map[string]string) {
	t.Helper()

	originalEnv := make(map[string]string)
	for k := range envs {
		if val, ok := os.LookupEnv(k); ok {
			originalEnv[k] = val
		} else {
			originalEnv[k] = ""
		}
	}

	for k, v := range envs {
		if err := os.Setenv(k, v); err != nil {
			t.Fatalf("Failed to set environment variable %q: %v", k, err)
		}
	}

	t.Cleanup(func() {
		for k := range envs {
			if originalVal, ok := originalEnv[k]; ok {
				if originalVal == "" {
					os.Unsetenv(k)
				} else {
					os.Setenv(k, originalVal)
				}
			}
		}
	})
}

// createTempFile creates a temporary .env file with given content in a temporary directory.
func createTempFile(t *testing.T, content string) (string, func()) {
	t.Helper()

	dir, err := os.MkdirTemp("", "envtest-*")
	if err != nil {
		t.Fatal("Failed to create temp dir:", err)
	}

	path := filepath.Join(dir, ".env")

	err = os.WriteFile(path, []byte(content), 0644)
	if err != nil {
		t.Fatal("Failed to write temp .env file:", err)
	}

	return path, func() {
		os.RemoveAll(dir)
	}
}

func TestLoadEnv(t *testing.T) {
	t.Run("loads_env_file_successfully", func(t *testing.T) {
		envContent := "KEY1=value1\nKEY2=value2"
		filePath, cleanup := createTempFile(t, envContent)
		defer cleanup()

		// Change working directory to temp dir where .env exists
		oldWd, _ := os.Getwd()
		defer os.Chdir(oldWd)
		os.Chdir(filepath.Dir(filePath))

		LoadEnv()

		val1 := os.Getenv("KEY1")
		val2 := os.Getenv("KEY2")

		if val1 != "value1" || val2 != "value2" {
			t.Errorf("Expected environment variables not loaded correctly")
		}
	})

	t.Run("logs_info_when_no_env_file", func(t *testing.T) {
		// Capture log output
		logOutput := captureLogs(t, func() {
			LoadEnv()
		})

		expected := "No .env file found, using system environment"
		if !strings.Contains(logOutput, expected) {
			t.Errorf("Expected log message containing %q, got %q", expected, logOutput)
		}
	})
}

// TestLoadEnvWithFile tests LoadEnv when a .env file exists
func TestLoadEnvWithFile(t *testing.T) {
	// Setup - create a temporary .env file
	dir := t.TempDir()
	envPath := filepath.Join(dir, ".env")
	err := os.WriteFile(envPath, []byte("TEST_KEY=test_value"), 0644)
	if err != nil {
		t.Fatalf("Failed to create .env file: %v", err)
	}

	// Change working directory to the temp dir
	oldWd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current working directory: %v", err)
	}
	defer os.Chdir(oldWd)
	os.Chdir(dir)

	// Test
	LoadEnv()

	// Verify
	if val := os.Getenv("TEST_KEY"); val != "test_value" {
		t.Errorf("Expected TEST_KEY to be 'test_value', got '%s'", val)
	}
}

// TestLoadEnvWithoutFile tests LoadEnv when no .env file exists
func TestLoadEnvWithoutFile(t *testing.T) {
	// Setup - use a clean temp dir with no .env file
	dir := t.TempDir()
	oldWd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current working directory: %v", err)
	}
	defer os.Chdir(oldWd)
	os.Chdir(dir)

	// Test - should not panic or fail
	LoadEnv()
}

func TestGetEnv(t *testing.T) {
	t.Run("returns_set_environment_variable", func(t *testing.T) {
		tempEnv(t, map[string]string{
			"TEST_VAR": "from_os",
		})

		got := GetEnv("TEST_VAR", "default")
		want := "from_os"

		if got != want {
			t.Errorf("GetEnv() = %v, want %v", got, want)
		}
	})

	t.Run("returns_fallback_if_environment_variable_not_set", func(t *testing.T) {
		// Ensure TEST_VAR is not set
		os.Unsetenv("TEST_VAR")

		got := GetEnv("TEST_VAR", "fallback_value")
		want := "fallback_value"

		if got != want {
			t.Errorf("GetEnv() = %v, want %v", got, want)
		}
	})

	t.Run("returns_empty_string_if_both_unset_and_no_fallback", func(t *testing.T) {
		os.Unsetenv("NON_EXISTENT_VAR")

		got := GetEnv("NON_EXISTENT_VAR", "")
		want := ""

		if got != want {
			t.Errorf("GetEnv() = %v, want %v", got, want)
		}
	})
}

// TestGetEnvWithSetVariable tests GetEnv when the variable is set
func TestGetEnvWithSetVariable(t *testing.T) {
	// Setup
	const testKey = "TEST_KEY_GETENV"
	const testValue = "test_value_123"
	os.Setenv(testKey, testValue)
	defer os.Unsetenv(testKey)

	// Test
	result := GetEnv(testKey, "default_value")

	// Verify
	if result != testValue {
		t.Errorf("Expected %s, got %s", testValue, result)
	}
}

// TestGetEnvWithUnsetVariable tests GetEnv when the variable is not set
func TestGetEnvWithUnsetVariable(t *testing.T) {
	// Setup
	const testKey = "NON_EXISTENT_KEY"
	const defaultValue = "default_value_456"
	os.Unsetenv(testKey) // Ensure it's not set

	// Test
	result := GetEnv(testKey, defaultValue)

	// Verify
	if result != defaultValue {
		t.Errorf("Expected default value %s, got %s", defaultValue, result)
	}
}

// TestGetEnvWithEmptyVariable tests GetEnv when the variable is set but empty
func TestGetEnvWithEmptyVariable(t *testing.T) {
	// Setup
	const testKey = "EMPTY_KEY"
	os.Setenv(testKey, "") // Set to empty string
	defer os.Unsetenv(testKey)

	const defaultValue = "default_value_789"

	// Test
	result := GetEnv(testKey, defaultValue)

	// Verify
	if result != "" {
		t.Errorf("Expected empty string, got %s", result)
	}
}

// TestLoadEnvWithMalformedFile tests LoadEnv with a malformed .env file
func TestLoadEnvWithMalformedFile(t *testing.T) {
	// Setup - create a malformed .env file
	dir := t.TempDir()
	envPath := filepath.Join(dir, ".env")
	err := os.WriteFile(envPath, []byte("THIS_IS_NOT_A_VALID_LINE\nTEST_KEY=test_value"), 0644)
	if err != nil {
		t.Fatalf("Failed to create .env file: %v", err)
	}

	// Change working directory to the temp dir
	oldWd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current working directory: %v", err)
	}
	defer os.Chdir(oldWd)
	os.Chdir(dir)

	// Test - should not panic
	LoadEnv()

	// Verify that valid entries are still loaded
	if val := os.Getenv("TEST_KEY"); val != "test_value" {
		t.Errorf("Expected TEST_KEY to be 'test_value', got '%s'", val)
	}
}

// TestGetEnvPriority tests that system env vars take precedence over .env file
func TestGetEnvPriority(t *testing.T) {
	// Setup - create .env file and set system env var
	dir := t.TempDir()
	envPath := filepath.Join(dir, ".env")
	err := os.WriteFile(envPath, []byte("PRIORITY_KEY=file_value"), 0644)
	if err != nil {
		t.Fatalf("Failed to create .env file: %v", err)
	}

	const testKey = "PRIORITY_KEY"
	const systemValue = "system_value"
	os.Setenv(testKey, systemValue)
	defer os.Unsetenv(testKey)

	// Change working directory to the temp dir
	oldWd, err := os.Getwd()
	if err != nil {
		t.Fatalf("Failed to get current working directory: %v", err)
	}
	defer os.Chdir(oldWd)
	os.Chdir(dir)

	// Load .env file
	LoadEnv()

	// Test GetEnv
	result := GetEnv(testKey, "default_value")

	// Verify system value takes precedence
	if result != systemValue {
		t.Errorf("Expected system value '%s', got '%s'", systemValue, result)
	}
}

// captureLogs captures log output from a function call.
func captureLogs(t *testing.T, f func()) string {
	t.Helper()

	// Redirect logs to a buffer
	oldOut := log.Writer()
	r, w, _ := os.Pipe()
	log.SetOutput(w)

	f() // Execute the function

	_ = w.Close() // Ensure all data is flushed
	log.SetOutput(oldOut)

	out, _ := io.ReadAll(r)
	return string(out)
}

func TestGetIntEnv(t *testing.T) {
	tests := []struct {
		name     string
		key      string
		fallback int
		envValue string
		expected int
	}{
		{
			name:     "valid integer",
			key:      "TEST_INT",
			fallback: 10,
			envValue: "42",
			expected: 42,
		},
		{
			name:     "invalid integer",
			key:      "TEST_INT_INVALID",
			fallback: 10,
			envValue: "not_a_number",
			expected: 10,
		},
		{
			name:     "negative integer",
			key:      "TEST_INT_NEGATIVE",
			fallback: 10,
			envValue: "-5",
			expected: -5,
		},
		{
			name:     "zero",
			key:      "TEST_INT_ZERO",
			fallback: 10,
			envValue: "0",
			expected: 0,
		},
		{
			name:     "non-existing key",
			key:      "NON_EXISTING_INT",
			fallback: 10,
			envValue: "",
			expected: 10,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.envValue != "" {
				tempEnv(t, map[string]string{tt.key: tt.envValue})
			} else {
				os.Unsetenv(tt.key)
			}

			result := GetIntEnv(tt.key, tt.fallback)
			if result != tt.expected {
				t.Errorf("GetIntEnv() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestGetDurationEnv(t *testing.T) {
	tests := []struct {
		name     string
		key      string
		fallback time.Duration
		envValue string
		expected time.Duration
	}{
		{
			name:     "valid duration seconds",
			key:      "TEST_DURATION_S",
			fallback: 10 * time.Second,
			envValue: "30s",
			expected: 30 * time.Second,
		},
		{
			name:     "valid duration minutes",
			key:      "TEST_DURATION_M",
			fallback: 10 * time.Second,
			envValue: "5m",
			expected: 5 * time.Minute,
		},
		{
			name:     "valid duration hours",
			key:      "TEST_DURATION_H",
			fallback: 10 * time.Second,
			envValue: "2h",
			expected: 2 * time.Hour,
		},
		{
			name:     "invalid duration",
			key:      "TEST_DURATION_INVALID",
			fallback: 10 * time.Second,
			envValue: "invalid_duration",
			expected: 10 * time.Second,
		},
		{
			name:     "non-existing key",
			key:      "NON_EXISTING_DURATION",
			fallback: 10 * time.Second,
			envValue: "",
			expected: 10 * time.Second,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.envValue != "" {
				tempEnv(t, map[string]string{tt.key: tt.envValue})
			} else {
				os.Unsetenv(tt.key)
			}

			result := GetDurationEnv(tt.key, tt.fallback)
			if result != tt.expected {
				t.Errorf("GetDurationEnv() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestGetBoolEnv(t *testing.T) {
	tests := []struct {
		name     string
		key      string
		fallback bool
		envValue string
		expected bool
	}{
		{
			name:     "true value",
			key:      "TEST_BOOL_TRUE",
			fallback: false,
			envValue: "true",
			expected: true,
		},
		{
			name:     "1 value",
			key:      "TEST_BOOL_1",
			fallback: false,
			envValue: "1",
			expected: true,
		},
		{
			name:     "yes value",
			key:      "TEST_BOOL_YES",
			fallback: false,
			envValue: "yes",
			expected: true,
		},
		{
			name:     "on value",
			key:      "TEST_BOOL_ON",
			fallback: false,
			envValue: "on",
			expected: true,
		},
		{
			name:     "false value",
			key:      "TEST_BOOL_FALSE",
			fallback: true,
			envValue: "false",
			expected: false,
		},
		{
			name:     "0 value",
			key:      "TEST_BOOL_0",
			fallback: true,
			envValue: "0",
			expected: false,
		},
		{
			name:     "no value",
			key:      "TEST_BOOL_NO",
			fallback: true,
			envValue: "no",
			expected: false,
		},
		{
			name:     "off value",
			key:      "TEST_BOOL_OFF",
			fallback: true,
			envValue: "off",
			expected: false,
		},
		{
			name:     "case insensitive TRUE",
			key:      "TEST_BOOL_UPPER",
			fallback: false,
			envValue: "TRUE",
			expected: true,
		},
		{
			name:     "case insensitive FALSE",
			key:      "TEST_BOOL_UPPER_FALSE",
			fallback: true,
			envValue: "FALSE",
			expected: false,
		},
		{
			name:     "invalid value",
			key:      "TEST_BOOL_INVALID",
			fallback: true,
			envValue: "maybe",
			expected: true,
		},
		{
			name:     "non-existing key",
			key:      "NON_EXISTING_BOOL",
			fallback: true,
			envValue: "",
			expected: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.envValue != "" {
				tempEnv(t, map[string]string{tt.key: tt.envValue})
			} else {
				os.Unsetenv(tt.key)
			}

			result := GetBoolEnv(tt.key, tt.fallback)
			if result != tt.expected {
				t.Errorf("GetBoolEnv() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestLoad(t *testing.T) {
	// Save original environment
	originalEnv := map[string]string{
		"SERVER_PORT":              os.Getenv("SERVER_PORT"),
		"WHATSAPP_DB_PATH":         os.Getenv("WHATSAPP_DB_PATH"),
		"LOG_LEVEL":                os.Getenv("LOG_LEVEL"),
		"LOG_FORMAT":               os.Getenv("LOG_FORMAT"),
		"SERVER_READ_TIMEOUT":      os.Getenv("SERVER_READ_TIMEOUT"),
		"SERVER_WRITE_TIMEOUT":     os.Getenv("SERVER_WRITE_TIMEOUT"),
		"SERVER_IDLE_TIMEOUT":      os.Getenv("SERVER_IDLE_TIMEOUT"),
		"WHATSAPP_QR_TIMEOUT":      os.Getenv("WHATSAPP_QR_TIMEOUT"),
		"WHATSAPP_RECONNECT_DELAY": os.Getenv("WHATSAPP_RECONNECT_DELAY"),
		"WHATSAPP_MAX_RECONNECTS":  os.Getenv("WHATSAPP_MAX_RECONNECTS"),
	}

	// Restore environment after test
	defer func() {
		for key, value := range originalEnv {
			if value != "" {
				os.Setenv(key, value)
			} else {
				os.Unsetenv(key)
			}
		}
		// Reset global config
		globalConfig = nil
	}()

	t.Run("load with defaults", func(t *testing.T) {
		// Clear all environment variables
		for key := range originalEnv {
			os.Unsetenv(key)
		}

		config, err := Load()
		if err != nil {
			t.Fatalf("Expected no error, got %v", err)
		}

		// Check default values
		if config.Server.Port != "8081" {
			t.Errorf("Expected default port 8081, got %s", config.Server.Port)
		}

		if config.Logging.Level != "info" {
			t.Errorf("Expected default log level info, got %s", config.Logging.Level)
		}

		if config.Logging.Format != "text" {
			t.Errorf("Expected default log format text, got %s", config.Logging.Format)
		}
	})

	t.Run("load with custom values", func(t *testing.T) {
		// Set custom environment variables
		os.Setenv("SERVER_PORT", "9090")
		os.Setenv("LOG_LEVEL", "debug")
		os.Setenv("LOG_FORMAT", "json")
		os.Setenv("SERVER_READ_TIMEOUT", "30s")
		os.Setenv("WHATSAPP_MAX_RECONNECTS", "10")

		config, err := Load()
		if err != nil {
			t.Fatalf("Expected no error, got %v", err)
		}

		// Check custom values
		if config.Server.Port != "9090" {
			t.Errorf("Expected port 9090, got %s", config.Server.Port)
		}

		if config.Logging.Level != "debug" {
			t.Errorf("Expected log level debug, got %s", config.Logging.Level)
		}

		if config.Logging.Format != "json" {
			t.Errorf("Expected log format json, got %s", config.Logging.Format)
		}

		if config.Server.ReadTimeout != 30*time.Second {
			t.Errorf("Expected read timeout 30s, got %v", config.Server.ReadTimeout)
		}

		if config.WhatsApp.MaxReconnects != 10 {
			t.Errorf("Expected max reconnects 10, got %d", config.WhatsApp.MaxReconnects)
		}
	})

	t.Run("load with invalid values", func(t *testing.T) {
		// Set invalid log level
		os.Setenv("LOG_LEVEL", "invalid_level")

		_, err := Load()
		if err == nil {
			t.Error("Expected error for invalid log level, got nil")
		}

		if !strings.Contains(err.Error(), "invalid log level") {
			t.Errorf("Expected error message about invalid log level, got %v", err)
		}
	})
}

func TestGet(t *testing.T) {
	// Reset global config
	globalConfig = nil

	// Clear environment variables for predictable test
	os.Unsetenv("SERVER_PORT")
	os.Unsetenv("LOG_LEVEL")

	config := Get()
	if config == nil {
		t.Fatal("Expected Get() to return a config")
	}

	// Should return same instance on subsequent calls
	config2 := Get()
	if config != config2 {
		t.Error("Expected Get() to return same instance")
	}

	// Reset for next test
	globalConfig = nil
}

func TestConfigValidate(t *testing.T) {
	tests := []struct {
		name        string
		config      *Config
		expectError bool
		errorText   string
	}{
		{
			name: "valid config",
			config: &Config{
				Server: ServerConfig{
					Port:         "8081",
					ReadTimeout:  15 * time.Second,
					WriteTimeout: 15 * time.Second,
					IdleTimeout:  60 * time.Second,
				},
				WhatsApp: WhatsAppConfig{
					DBPath:         "file:session.db",
					QRTimeout:      2 * time.Minute,
					ReconnectDelay: 5 * time.Second,
					MaxReconnects:  5,
				},
				Logging: LoggingConfig{
					Level:  "info",
					Format: "text",
				},
				WebSocket: WebSocketConfig{
					Enabled:             true,
					MaxClients:          1000,
					ReadBufferSize:      1024,
					WriteBufferSize:     1024,
					EnableCompression:   true,
					ClientTimeout:       5 * time.Minute,
					CleanupInterval:     30 * time.Second,
					MetricsInterval:     10 * time.Second,
					BroadcastBufferSize: 1000,
					AllowedOrigins:      []string{"*"},
					EnableAuth:          false,
					RateLimit:           10,
					RateLimitWindow:     1 * time.Minute,
				},
			},
			expectError: false,
		},
		{
			name: "empty server port",
			config: &Config{
				Server: ServerConfig{
					Port:         "",
					ReadTimeout:  15 * time.Second,
					WriteTimeout: 15 * time.Second,
					IdleTimeout:  60 * time.Second,
				},
				WhatsApp: WhatsAppConfig{
					DBPath:         "file:session.db",
					QRTimeout:      2 * time.Minute,
					ReconnectDelay: 5 * time.Second,
					MaxReconnects:  5,
				},
				Logging: LoggingConfig{
					Level:  "info",
					Format: "text",
				},
			},
			expectError: true,
			errorText:   "server port cannot be empty",
		},
		{
			name: "invalid log level",
			config: &Config{
				Server: ServerConfig{
					Port:         "8081",
					ReadTimeout:  15 * time.Second,
					WriteTimeout: 15 * time.Second,
					IdleTimeout:  60 * time.Second,
				},
				WhatsApp: WhatsAppConfig{
					DBPath:         "file:session.db",
					QRTimeout:      2 * time.Minute,
					ReconnectDelay: 5 * time.Second,
					MaxReconnects:  5,
				},
				Logging: LoggingConfig{
					Level:  "invalid",
					Format: "text",
				},
			},
			expectError: true,
			errorText:   "invalid log level",
		},
		{
			name: "invalid log format",
			config: &Config{
				Server: ServerConfig{
					Port:         "8081",
					ReadTimeout:  15 * time.Second,
					WriteTimeout: 15 * time.Second,
					IdleTimeout:  60 * time.Second,
				},
				WhatsApp: WhatsAppConfig{
					DBPath:         "file:session.db",
					QRTimeout:      2 * time.Minute,
					ReconnectDelay: 5 * time.Second,
					MaxReconnects:  5,
				},
				Logging: LoggingConfig{
					Level:  "info",
					Format: "invalid",
				},
			},
			expectError: true,
			errorText:   "invalid log format",
		},
		{
			name: "negative timeout",
			config: &Config{
				Server: ServerConfig{
					Port:         "8081",
					ReadTimeout:  -1 * time.Second,
					WriteTimeout: 15 * time.Second,
					IdleTimeout:  60 * time.Second,
				},
				WhatsApp: WhatsAppConfig{
					DBPath:         "file:session.db",
					QRTimeout:      2 * time.Minute,
					ReconnectDelay: 5 * time.Second,
					MaxReconnects:  5,
				},
				Logging: LoggingConfig{
					Level:  "info",
					Format: "text",
				},
			},
			expectError: true,
			errorText:   "server read timeout must be positive",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.config.Validate()

			if tt.expectError {
				if err == nil {
					t.Error("Expected error, got nil")
				} else if !strings.Contains(err.Error(), tt.errorText) {
					t.Errorf("Expected error containing %q, got %v", tt.errorText, err)
				}
			} else {
				if err != nil {
					t.Errorf("Expected no error, got %v", err)
				}
			}
		})
	}
}
