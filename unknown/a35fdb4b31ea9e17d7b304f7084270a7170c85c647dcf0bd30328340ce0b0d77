import structlog
from typing import <PERSON><PERSON>

from datetime import datetime

from schemas.config import AIConfig
from schemas.responses import AIResponse

logger = structlog.get_logger()


class AIService:
    """Professional AI service with structured logging and monitoring"""

    def __init__(self, api_key: str, config: Optional[AIConfig] = None):
        """
        Initialize AI service with API key and optional configuration

        Args:
            api_key: API key for the AI service
            config: Configuration parameters for AI behavior
        """
        self.api_key = api_key
        self.config = config or AIConfig()
        self._initialize_client()

        logger.info(
            "AI Service initialized",
            model=self.config.model,
            temperature=self.config.temperature,
        )

    def _initialize_client(self):
        """Initialize the AI client with error handling"""
        try:
            from google import genai

            self.client = genai.Client(api_key=self.api_key)
            logger.debug("AI client initialized successfully")
        except ImportError as e:
            logger.error("Failed to import AI client library", error=str(e))
            raise
        except Exception as e:
            logger.error("AI client initialization failed", error=str(e))
            raise RuntimeError("AI service unavailable") from e

    def generate_response(self, prompt: str) -> AIResponse:
        """
        Generate response from AI service with full logging

        Args:
            prompt: Input prompt for the AI

        Returns:
            AIResponse: Structured response object

        Raises:
            RuntimeError: If generation fails
        """
        try:
            logger.info(
                "Generating AI response",
                prompt_length=len(prompt),
                model=self.config.model,
            )

            start_time = datetime.now()

            # Import types for proper API call
            from google.genai import types

            response = self.client.models.generate_content(
                model=self.config.model,
                contents=prompt,  # Pass as string, not list
                config=types.GenerateContentConfig(
                    system_instruction=self.config.system_prompt,
                    temperature=self.config.temperature,
                    max_output_tokens=self.config.max_tokens,
                ),
            )

            duration = (datetime.now() - start_time).total_seconds()

            # Debug logging to understand the response structure
            logger.debug(
                "Raw AI response details",
                response_type=type(response).__name__,
                has_text=hasattr(response, "text"),
                text_value=getattr(response, "text", None),
                has_candidates=hasattr(response, "candidates"),
                candidates_count=len(response.candidates)
                if hasattr(response, "candidates") and response.candidates
                else 0,
            )

            # Try to extract content from response
            content = None
            if hasattr(response, "text") and response.text:
                content = response.text
            elif hasattr(response, "candidates") and response.candidates:
                # Try to get content from first candidate
                first_candidate = response.candidates[0]
                logger.debug(
                    "Examining first candidate",
                    has_content=hasattr(first_candidate, "content"),
                    finish_reason=getattr(first_candidate, "finish_reason", None),
                )

                if hasattr(first_candidate, "content") and first_candidate.content:
                    candidate_content = first_candidate.content
                    logger.debug(
                        "Candidate content details",
                        has_parts=hasattr(candidate_content, "parts"),
                        parts_count=len(candidate_content.parts)
                        if hasattr(candidate_content, "parts")
                        and candidate_content.parts
                        else 0,
                    )

                    if hasattr(candidate_content, "parts") and candidate_content.parts:
                        # Get text from first part
                        first_part = candidate_content.parts[0]
                        logger.debug(
                            "First part details",
                            part_type=type(first_part).__name__,
                            has_text=hasattr(first_part, "text"),
                            text_value=getattr(first_part, "text", None),
                        )
                        if hasattr(first_part, "text") and first_part.text:
                            content = first_part.text

            # Check if we got content or if the response was truncated
            if not content:
                # Check if response was truncated due to token limits
                if (
                    hasattr(response, "candidates")
                    and response.candidates
                    and hasattr(response.candidates[0], "finish_reason")
                    and response.candidates[0].finish_reason == "MAX_TOKENS"
                ):
                    content = "[Response truncated due to token limit - please increase max_tokens setting]"
                    logger.warning("Response truncated due to MAX_TOKENS limit")
                else:
                    content = "[No response content available]"
                    logger.warning(
                        "No content found in AI response",
                        response_structure=str(response),
                    )

            logger.info(
                "AI response generated",
                duration_sec=duration,
                response_length=len(content),
                content_found=content != "[No response content available]",
            )

            return AIResponse(
                content=content,
                model=self.config.model,
                timestamp=datetime.now(),
                metadata={
                    "temperature": self.config.temperature,
                    "tokens_used": len(content.split())
                    if content and content != "[No response content available]"
                    else 0,
                },
            )

        except Exception as e:
            logger.error(
                "AI generation failed",
                error=str(e),
                prompt_sample=prompt[:100] + "..." if len(prompt) > 100 else prompt,
            )
            raise RuntimeError("Failed to generate AI response") from e

    def health_check(self) -> bool:
        """Check if the AI service is healthy"""
        try:
            self.client.models.list()
            logger.debug("AI health check passed")
            return True
        except Exception as e:
            logger.warning("AI health check failed", error=str(e))
            return False
