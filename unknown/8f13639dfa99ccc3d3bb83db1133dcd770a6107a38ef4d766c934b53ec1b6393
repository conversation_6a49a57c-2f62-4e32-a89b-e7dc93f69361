from datetime import datetime
from typing import Optional
from pydantic import BaseModel


class ExplanationRequest(BaseModel):
    prompt: str
    temperature: Optional[float] = None
    max_tokens: Optional[int] = None


class ExplanationResponse(BaseModel):
    content: str
    model: str
    timestamp: datetime
    tokens_used: int
    processing_time_ms: float


class HealthCheckResponse(BaseModel):
    status: str
    model: str
    version: str
