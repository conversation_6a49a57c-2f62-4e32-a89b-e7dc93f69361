from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
# from fastapi.staticfiles import StaticFiles

from config.settings import get_settings
from api.routes import router as api_router


def create_app() -> FastAPI:
    """Application factory"""
    settings = get_settings()

    # Initialize FastAPI
    app = FastAPI(
        title=settings.app_name,
        version=settings.version,
        description="Wellbot AI Service API",
        docs_url="/docs" if settings.debug else None,
        redoc_url=None,
    )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include routes
    app.include_router(api_router)

    # Mount static files if in production
    # if not settings.debug:
    #     app.mount("/static", StaticFiles(directory="static"), name="static")

    return app


app = create_app()
