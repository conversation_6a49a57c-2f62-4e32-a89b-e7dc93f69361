@baseUrl = http://localhost:8000/api/v1
@apiKey = wellbot-dev-key-2025

### Health Check
GET {{baseUrl}}/health
X-API-Key: {{apiKey}}

### Generate Explanation (Basic)
POST {{baseUrl}}/explanations
Content-Type: application/json
X-API-Key: {{apiKey}}

{
  "prompt": "Explain quantum computing in simple terms"
}

### Generate Explanation (Custom Parameters)
POST {{baseUrl}}/explanations
Content-Type: application/json
X-API-Key: {{apiKey}}

{
  "prompt": "Explain blockchain technology",
  "temperature": 0.5,
  "max_tokens": 500
}

### Invalid API Key (401 Test)
POST {{baseUrl}}/explanations
Content-Type: application/json
X-API-Key: invalid_key

{
  "prompt": "This should fail"
}

### Malformed Request (422 Test)
POST {{baseUrl}}/explanations
Content-Type: application/json
X-API-Key: {{apiKey}}

{
  "temperature": 2.5  # Invalid value (should be 0-1)
}

### Edge Case - Empty Prompt
POST {{baseUrl}}/explanations
Content-Type: application/json
X-API-Key: {{apiKey}}

{
  "prompt": ""
}

### Edge Case - Very Long Prompt
POST {{baseUrl}}/explanations
Content-Type: application/json
X-API-Key: {{apiKey}}

{
  "prompt": "Lorem ipsum dolor sit amet... (1000+ chars)"
}