import time
from fastapi import <PERSON>Router, Depends, HTTPException
from typing import Annotated

from config.settings import Settings
from services.ai_service import AIService
from api.schemas import (
    ExplanationRequest,
    ExplanationResponse,
    HealthCheckResponse,
)
from api.dependencies import get_ai_service, verify_api_key
from config.settings import get_settings

router = APIRouter(
    prefix="/api/v1", tags=["AI Service"], dependencies=[Depends(verify_api_key)]
)


@router.post(
    "/explanations",
    response_model=ExplanationResponse,
    summary="Generate AI explanation",
    description="Generates an AI-powered explanation for the given prompt",
)
async def generate_explanation(
    request: ExplanationRequest,
    ai_service: Annotated[AIService, Depends(get_ai_service)],
) -> ExplanationResponse:
    try:
        start_time = time.time()
        response = ai_service.generate_response(request.prompt)
        processing_time_ms = (time.time() - start_time) * 1000

        return ExplanationResponse(
            content=response.content,
            model=response.model,
            timestamp=response.timestamp,
            tokens_used=response.metadata.get("tokens_used", 0),
            processing_time_ms=processing_time_ms,
        )
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Explanation generation failed: {str(e)}"
        )


@router.get(
    "/health",
    response_model=HealthCheckResponse,
    summary="Service health check",
    description="Returns the current health status of the AI service",
)
async def health_check(
    ai_service: Annotated[AIService, Depends(get_ai_service)],
    settings: Annotated[Settings, Depends(get_settings)],
) -> HealthCheckResponse:
    is_healthy = ai_service.health_check()
    return HealthCheckResponse(
        status="OK" if is_healthy else "UNHEALTHY",
        model=settings.gemini_model,
        version=settings.version,
    )
