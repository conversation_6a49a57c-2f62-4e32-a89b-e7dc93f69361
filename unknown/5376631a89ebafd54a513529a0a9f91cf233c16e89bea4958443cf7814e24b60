package server

import (
	"context"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
	"time"
)

func TestNew(t *testing.T) {
	// Set up environment for testing
	os.Setenv("SERVER_PORT", "8082")
	defer os.Unsetenv("SERVER_PORT")

	server := New()

	if server == nil {
		t.Fatal("Expected New() to return a server instance")
	}

	if server.port != "8082" {
		t.<PERSON><PERSON><PERSON>("Expected port 8082, got %s", server.port)
	}

	if server.router == nil {
		t.Error("Expected router to be initialized")
	}

	if server.httpServer == nil {
		t.Error("Expected HTTP server to be initialized")
	}

	// Test default port
	os.Unsetenv("SERVER_PORT")
	server2 := New()
	if server2.port != "8081" {
		t.<PERSON>rrorf("Expected default port 8081, got %s", server2.port)
	}
}

func TestServer_GetPort(t *testing.T) {
	server := &Server{port: "9999"}

	if server.GetPort() != "9999" {
		t.<PERSON>("Expected port 9999, got %s", server.GetPort())
	}
}

func TestServer_SetupRoutes(t *testing.T) {
	server := New()

	// Test that routes are properly set up by making requests
	testCases := []struct {
		method string
		path   string
		status int
	}{
		{"GET", "/api/health", http.StatusOK},
		{"GET", "/api/status", http.StatusOK},
		{"GET", "/api/metrics", http.StatusOK},
		{"POST", "/api/send", http.StatusBadRequest}, // Will fail validation but route exists
	}

	for _, tc := range testCases {
		t.Run(tc.method+" "+tc.path, func(t *testing.T) {
			req := httptest.NewRequest(tc.method, tc.path, nil)
			rr := httptest.NewRecorder()

			server.router.ServeHTTP(rr, req)

			// Check that the route exists (not 404)
			if rr.Code == http.StatusNotFound {
				t.Errorf("Route %s %s not found", tc.method, tc.path)
			}
		})
	}
}

func TestServer_MethodNotAllowed(t *testing.T) {
	server := New()

	// Test method not allowed for health endpoint
	req := httptest.NewRequest("POST", "/api/health", nil)
	rr := httptest.NewRecorder()

	server.router.ServeHTTP(rr, req)

	// Gorilla mux returns 404 for method not allowed by default
	// This is expected behavior unless we configure method not allowed handler
	if rr.Code != http.StatusNotFound && rr.Code != http.StatusMethodNotAllowed {
		t.Errorf("Expected status 404 or 405 for POST /api/health, got %d", rr.Code)
	}
}

func TestMonitoringMiddleware(t *testing.T) {
	// Create a test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("test response"))
	})

	// Wrap with monitoring middleware
	wrappedHandler := monitoringMiddleware(testHandler)

	// Test successful request
	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr, req)

	if rr.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", rr.Code)
	}

	// Test error request
	errorHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusInternalServerError)
	})

	wrappedErrorHandler := monitoringMiddleware(errorHandler)

	req2 := httptest.NewRequest("GET", "/test", nil)
	rr2 := httptest.NewRecorder()

	wrappedErrorHandler.ServeHTTP(rr2, req2)

	if rr2.Code != http.StatusInternalServerError {
		t.Errorf("Expected status 500, got %d", rr2.Code)
	}
}

func TestLoggingMiddleware(t *testing.T) {
	// Create a test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	// Wrap with logging middleware
	wrappedHandler := loggingMiddleware(testHandler)

	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr, req)

	if rr.Code != http.StatusOK {
		t.Errorf("Expected status 200, got %d", rr.Code)
	}

	// The logging middleware should not affect the response
	// We can't easily test the log output without more complex setup
}

func TestCorsMiddleware(t *testing.T) {
	// Create a test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	// Wrap with CORS middleware
	wrappedHandler := corsMiddleware(testHandler)

	// Test regular request
	req := httptest.NewRequest("GET", "/test", nil)
	rr := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr, req)

	// Check CORS headers
	if rr.Header().Get("Access-Control-Allow-Origin") != "*" {
		t.Error("Expected Access-Control-Allow-Origin header to be set")
	}

	if rr.Header().Get("Access-Control-Allow-Methods") == "" {
		t.Error("Expected Access-Control-Allow-Methods header to be set")
	}

	if rr.Header().Get("Access-Control-Allow-Headers") == "" {
		t.Error("Expected Access-Control-Allow-Headers header to be set")
	}

	// Test OPTIONS request
	optionsReq := httptest.NewRequest("OPTIONS", "/test", nil)
	optionsRr := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(optionsRr, optionsReq)

	if optionsRr.Code != http.StatusOK {
		t.Errorf("Expected status 200 for OPTIONS request, got %d", optionsRr.Code)
	}
}

func TestContentTypeMiddleware(t *testing.T) {
	// Create a test handler
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
	})

	// Wrap with content type middleware
	wrappedHandler := contentTypeMiddleware(testHandler)

	// Test API request
	req := httptest.NewRequest("GET", "/api/test", nil)
	rr := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(rr, req)

	if rr.Header().Get("Content-Type") != "application/json" {
		t.Errorf("Expected Content-Type application/json for API request, got %s", rr.Header().Get("Content-Type"))
	}

	// Test non-API request
	nonApiReq := httptest.NewRequest("GET", "/health", nil)
	nonApiRr := httptest.NewRecorder()

	wrappedHandler.ServeHTTP(nonApiRr, nonApiReq)

	// Should not set content type for non-API requests
	if nonApiRr.Header().Get("Content-Type") != "" {
		t.Errorf("Expected no Content-Type for non-API request, got %s", nonApiRr.Header().Get("Content-Type"))
	}

	// Test WebSocket endpoint (this was causing the panic)
	wsReq := httptest.NewRequest("GET", "/ws", nil)
	wsRr := httptest.NewRecorder()

	// This should not panic
	wrappedHandler.ServeHTTP(wsRr, wsReq)

	// Should not set content type for WebSocket requests
	if wsRr.Header().Get("Content-Type") != "" {
		t.Errorf("Expected no Content-Type for WebSocket request, got %s", wsRr.Header().Get("Content-Type"))
	}

	// Test short path (edge case that could cause panic)
	shortReq := httptest.NewRequest("GET", "/a", nil)
	shortRr := httptest.NewRecorder()

	// This should not panic
	wrappedHandler.ServeHTTP(shortRr, shortReq)

	// Should not set content type for short paths
	if shortRr.Header().Get("Content-Type") != "" {
		t.Errorf("Expected no Content-Type for short path request, got %s", shortRr.Header().Get("Content-Type"))
	}
}

func TestResponseWriter(t *testing.T) {
	rr := httptest.NewRecorder()
	wrapper := &responseWriter{
		ResponseWriter: rr,
		statusCode:     http.StatusOK,
	}

	// Test WriteHeader
	wrapper.WriteHeader(http.StatusNotFound)

	if wrapper.statusCode != http.StatusNotFound {
		t.Errorf("Expected status code 404, got %d", wrapper.statusCode)
	}

	if rr.Code != http.StatusNotFound {
		t.Errorf("Expected underlying response writer status 404, got %d", rr.Code)
	}
}

func TestServer_Stop(t *testing.T) {
	server := New()

	// Start server in background
	go func() {
		server.Start()
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Test graceful shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	err := server.Stop(ctx)
	if err != nil {
		t.Errorf("Expected no error during shutdown, got %v", err)
	}
}

func TestServer_Integration(t *testing.T) {
	// Test that the server can handle actual HTTP requests
	server := New()

	// Start server in background
	go func() {
		server.Start()
	}()

	// Give server time to start
	time.Sleep(100 * time.Millisecond)

	// Make a test request
	req := httptest.NewRequest("GET", "/api/health", nil)
	rr := httptest.NewRecorder()

	server.router.ServeHTTP(rr, req)

	if rr.Code != http.StatusServiceUnavailable && rr.Code != http.StatusOK {
		t.Errorf("Expected status 200 or 503 for health check, got %d", rr.Code)
	}

	// Check that response is JSON
	contentType := rr.Header().Get("Content-Type")
	if !strings.Contains(contentType, "application/json") {
		t.Errorf("Expected JSON content type, got %s", contentType)
	}

	// Shutdown server
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	server.Stop(ctx)
}

func TestServer_MiddlewareOrder(t *testing.T) {
	// Test that middleware is applied in the correct order
	server := New()

	req := httptest.NewRequest("GET", "/api/health", nil)
	rr := httptest.NewRecorder()

	server.router.ServeHTTP(rr, req)

	// Check that CORS headers are set (CORS middleware)
	if rr.Header().Get("Access-Control-Allow-Origin") == "" {
		t.Error("CORS middleware not applied")
	}

	// Check that content type is set for API routes (content type middleware)
	if !strings.Contains(rr.Header().Get("Content-Type"), "application/json") {
		t.Error("Content type middleware not applied")
	}
}

func TestServer_ConfigurationIntegration(t *testing.T) {
	// Test server configuration integration
	originalPort := os.Getenv("SERVER_PORT")
	defer func() {
		if originalPort != "" {
			os.Setenv("SERVER_PORT", originalPort)
		} else {
			os.Unsetenv("SERVER_PORT")
		}
	}()

	// Test custom port configuration
	os.Setenv("SERVER_PORT", "9876")

	server := New()

	if server.GetPort() != "9876" {
		t.Errorf("Expected port 9876 from environment, got %s", server.GetPort())
	}

	// Test that server address is correctly set
	expectedAddr := ":9876"
	if server.httpServer.Addr != expectedAddr {
		t.Errorf("Expected server address %s, got %s", expectedAddr, server.httpServer.Addr)
	}
}
