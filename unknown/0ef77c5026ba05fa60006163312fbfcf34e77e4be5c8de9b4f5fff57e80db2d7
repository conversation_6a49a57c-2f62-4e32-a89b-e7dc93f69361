from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.theme import Theme

from config.settings import get_settings


class Emoji:
    ROBOT = "🤖"
    MAGNIFYING_GLASS = "🔍"
    SPARKLES = "✨"
    WARNING = "⚠️"
    CROSS = "❌"


CUSTOM_THEME = Theme(
    {
        "info": "dim cyan",
        "warning": "magenta",
        "error": "bold red",
        "success": "bold green",
        "title": "bold blue",
        "subtitle": "dim blue",
    }
)


class ConsoleRenderer:
    """Rich console output manager"""

    def __init__(self):
        self.console = Console(theme=CUSTOM_THEME)

    def show_header(self) -> None:
        """Display application header"""
        settings = get_settings()
        self.console.print(
            Panel.fit(
                Text(
                    f"{Emoji.ROBOT} {settings.app_name}",
                    style="title",
                    justify="center",
                ),
                subtitle=f"v{settings.version}",
                border_style="blue",
                subtitle_align="center",
            )
        )

    def show_processing(self, message: str = "Processing...") -> None:
        """Show animated progress indicator"""
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            transient=True,
            console=self.console,
        ) as progress:
            progress.add_task(
                description=f"{Emoji.MAGNIFYING_GLASS} {message}", total=None
            )

    def show_result(self, title: str, content: str) -> None:
        """Display formatted result"""
        self.console.print(
            Panel.fit(
                Text(content, justify="center"),
                title=f"{Emoji.SPARKLES} {title}",
                border_style="green",
                title_align="center",
            )
        )

    def show_error(self, title: str, message: str) -> None:
        """Display formatted error"""
        self.console.print(
            Panel.fit(
                Text.from_markup(f"{Emoji.CROSS} [error]{title}[/error]\n\n{message}"),
                border_style="red",
            )
        )
