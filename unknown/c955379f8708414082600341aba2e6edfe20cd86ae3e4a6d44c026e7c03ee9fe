/*!
# Configuration Module

Contains all configuration related code for the bridge.
*/

use std::time::Duration;

use anyhow::Result;
use serde::{Deserialize, Serialize};
use tracing::{info, warn};

/// Chat-port service configuration
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ChatPortConfig {
    /// WebSocket URL for receiving messages
    pub websocket_url: String,
    /// HTTP API base URL for sending messages
    pub api_base_url: String,
    /// Connection timeout in seconds
    pub connection_timeout_secs: u64,
    /// Reconnection delay in seconds
    pub reconnect_delay_secs: u64,
    /// Maximum reconnection attempts
    pub max_reconnect_attempts: u32,
    /// Heartbeat interval in seconds
    pub heartbeat_interval_secs: u64,
}

/// AI service (genuis) configuration
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct AiServiceConfig {
    /// Base URL for AI service
    pub base_url: String,
    /// API key for authentication
    pub api_key: String,
    /// Request timeout in seconds
    pub timeout_secs: u64,
    /// Retry attempts for failed requests
    pub retry_attempts: u32,
    /// Retry delay in seconds
    pub retry_delay_secs: u64,
    /// Default temperature for AI requests
    pub default_temperature: Option<f32>,
    /// Default max tokens for AI requests
    pub default_max_tokens: Option<i32>,
}

/// Bridge service configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BridgeConfig {
    /// Service name
    pub service_name: String,
    /// Service version
    pub version: String,
    /// Maximum concurrent message processing
    pub max_concurrent_messages: usize,
    /// Message processing timeout in seconds
    pub message_timeout_secs: u64,
    /// Rate limit: messages per minute
    pub rate_limit_per_minute: u32,
    /// Enable message deduplication
    pub enable_deduplication: bool,
    /// Deduplication window in seconds
    pub deduplication_window_secs: u64,
}

impl BridgeConfig {
    /// Get message timeout as Duration
    pub fn message_timeout(&self) -> Duration {
        Duration::from_secs(self.message_timeout_secs)
    }

    /// Get deduplication window as Duration
    pub fn deduplication_window(&self) -> Duration {
        Duration::from_secs(self.deduplication_window_secs)
    }
}

/// Main configuration structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// Chat-port service configuration
    pub chat_port: ChatPortConfig,
    /// AI service (genuis) configuration
    pub ai_service: AiServiceConfig,
    /// Bridge service configuration
    pub bridge: BridgeConfig,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            chat_port: ChatPortConfig {
                websocket_url: "ws://localhost:8081/ws".into(),
                api_base_url: "http://localhost:8081/api".into(),
                connection_timeout_secs: 30,
                reconnect_delay_secs: 5,
                max_reconnect_attempts: 10,
                heartbeat_interval_secs: 30,
            },
            ai_service: AiServiceConfig {
                base_url: "http://localhost:8000/api/v1".to_string(),
                api_key: "wellbot-dev-key-2025".to_string(),
                timeout_secs: 60,
                retry_attempts: 3,
                retry_delay_secs: 2,
                default_temperature: Some(0.7),
                default_max_tokens: Some(4000i32),
            },
            bridge: BridgeConfig {
                service_name: "wellbot-bridge".to_string(),
                version: "0.1.0".to_string(),
                max_concurrent_messages: 10,
                message_timeout_secs: 30,
                rate_limit_per_minute: 60,
                enable_deduplication: true,
                deduplication_window_secs: 300,
            },
        }
    }
}

impl Config {
    /// Validate configuration
    pub fn validate(&self) -> Result<()> {
        Ok(())
    }

    /// Load configuration from environment variables
    pub fn from_env() -> Result<Self> {
        // Load .env file if it exists
        if let Err(e) = dotenvy::dotenv() {
            warn!("No .env file found or error loading it: {}", e);
        }

        let config = Self {
            chat_port: ChatPortConfig {
                websocket_url: get_env("CHAT_PORT_WEBSOCKET_URL", "ws://localhost:8081/ws".into()),
                api_base_url: get_env("CHAT_PORT_API_URL", "http://localhost:8081/api".into()),
                connection_timeout_secs: get_env("CHAT_PORT_CONNECTION_TIMEOUT", 30),
                reconnect_delay_secs: get_env("CHAT_PORT_RECONNECT_DELAY", 5),
                max_reconnect_attempts: get_env("CHAT_PORT_MAX_RECONNECTS", 10),
                heartbeat_interval_secs: get_env("CHAT_PORT_HEARTBEAT_INTERVAL", 30),
            },
            ai_service: AiServiceConfig {
                base_url: get_env("AI_SERVICE_URL", "http://localhost:8000/api/v1".into()),
                api_key: get_env("AI_SERVICE_API_KEY", "wellbot-dev-key-2025".into()),
                timeout_secs: get_env("AI_SERVICE_TIMEOUT", 60),
                retry_attempts: get_env("AI_SERVICE_RETRY_ATTEMPTS", 3),
                retry_delay_secs: get_env("AI_SERVICE_RETRY_DELAY", 2),
                default_temperature: get_env_opt("AI_SERVICE_DEFAULT_TEMPERATURE"),
                default_max_tokens: get_env_opt("AI_SERVICE_DEFAULT_MAX_TOKENS"),
            },
            bridge: BridgeConfig {
                service_name: get_env("BRIDGE_SERVICE_NAME", "wellbot-bridge".into()),
                version: get_env("BRIDGE_VERSION", env!("CARGO_PKG_VERSION").into()),
                max_concurrent_messages: get_env("BRIDGE_MAX_CONCURRENT", 100),
                message_timeout_secs: get_env("BRIDGE_MESSAGE_TIMEOUT", 120),
                rate_limit_per_minute: get_env("BRIDGE_RATE_LIMIT", 60),
                enable_deduplication: get_env("BRIDGE_ENABLE_DEDUP", true),
                deduplication_window_secs: get_env("BRIDGE_DEDUP_WINDOW", 300),
            },
        };

        config.validate()?;
        info!("Configuration loaded and validated successfully");

        Ok(config)
    }
}

// Helper functions for environment variable parsing
fn get_env<T>(key: &str, default: T) -> T
where
    T: std::str::FromStr + Clone,
{
    std::env::var(key)
        .ok()
        .and_then(|v| v.parse().ok())
        .unwrap_or(default)
}

fn get_env_opt<T>(key: &str) -> Option<T>
where
    T: std::str::FromStr,
{
    std::env::var(key).ok().and_then(|v| v.parse().ok())
}
