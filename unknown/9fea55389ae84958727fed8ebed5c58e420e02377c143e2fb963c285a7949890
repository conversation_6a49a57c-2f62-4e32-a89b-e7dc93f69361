use sea_orm_migration::{DbErr, Mi<PERSON><PERSON><PERSON><PERSON>, Mi<PERSON><PERSON>Trait, sea_orm::DatabaseConnection};


pub struct Migrator;

#[async_trait::async_trait]
impl MigratorTrait for Migrator {
    fn migrations() -> Vec<Box<dyn MigrationTrait>> {
        vec![
            
        ]
    }
}

pub async fn run_migrations(db: &DatabaseConnection) -> Result<(), DbErr> {
    Migrator::up(db, None).await
}
