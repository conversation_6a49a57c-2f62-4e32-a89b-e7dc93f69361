package logger

import (
	"bytes"
	"log"
	"strings"
	"testing"

	"chatport-go/config"
)

// captureLogOutput captures log output for testing
func captureLogOutput(t *testing.T, fn func()) string {
	t.Helper()

	// Save original log output
	originalOutput := log.Writer()

	// Create a buffer to capture output
	var buf bytes.Buffer
	log.SetOutput(&buf)

	// Restore original output after test
	defer log.SetOutput(originalOutput)

	// Execute the function
	fn()

	return buf.String()
}

func TestInit(t *testing.T) {
	tests := []struct {
		name   string
		config *config.Config
	}{
		{
			name: "text format logger",
			config: &config.Config{
				Logging: config.LoggingConfig{
					Level:  "info",
					Format: "text",
				},
			},
		},
		{
			name: "json format logger",
			config: &config.Config{
				Logging: config.LoggingConfig{
					Level:  "debug",
					Format: "json",
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset global logger
			globalLogger = nil

			Init(tt.config)

			if globalLogger == nil {
				t.Error("Expected global logger to be initialized")
			}

			if globalLogger.level != strings.ToLower(tt.config.Logging.Level) {
				t.Errorf("Expected level %s, got %s", tt.config.Logging.Level, globalLogger.level)
			}

			if globalLogger.format != strings.ToLower(tt.config.Logging.Format) {
				t.Errorf("Expected format %s, got %s", tt.config.Logging.Format, globalLogger.format)
			}
		})
	}
}

func TestGet(t *testing.T) {
	// Reset global logger
	globalLogger = nil

	logger := Get()

	if logger == nil {
		t.Error("Expected Get() to return a logger")
	}

	// Should return default values when not initialized
	if logger.level != LevelInfo {
		t.Errorf("Expected default level %s, got %s", LevelInfo, logger.level)
	}

	if logger.format != "text" {
		t.Errorf("Expected default format text, got %s", logger.format)
	}

	// Should return same instance on subsequent calls
	logger2 := Get()
	if logger != logger2 {
		t.Error("Expected Get() to return same instance")
	}
}

func TestLogger_ShouldLog(t *testing.T) {
	tests := []struct {
		name         string
		loggerLevel  string
		messageLevel string
		shouldLog    bool
	}{
		{"debug logger logs debug", LevelDebug, LevelDebug, true},
		{"debug logger logs info", LevelDebug, LevelInfo, true},
		{"debug logger logs warn", LevelDebug, LevelWarn, true},
		{"debug logger logs error", LevelDebug, LevelError, true},
		{"info logger skips debug", LevelInfo, LevelDebug, false},
		{"info logger logs info", LevelInfo, LevelInfo, true},
		{"info logger logs warn", LevelInfo, LevelWarn, true},
		{"info logger logs error", LevelInfo, LevelError, true},
		{"warn logger skips debug", LevelWarn, LevelDebug, false},
		{"warn logger skips info", LevelWarn, LevelInfo, false},
		{"warn logger logs warn", LevelWarn, LevelWarn, true},
		{"warn logger logs error", LevelWarn, LevelError, true},
		{"error logger skips debug", LevelError, LevelDebug, false},
		{"error logger skips info", LevelError, LevelInfo, false},
		{"error logger skips warn", LevelError, LevelWarn, false},
		{"error logger logs error", LevelError, LevelError, true},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logger := &Logger{
				level:  tt.loggerLevel,
				format: "text",
				prefix: "test",
			}

			result := logger.shouldLog(tt.messageLevel)
			if result != tt.shouldLog {
				t.Errorf("Expected shouldLog to return %v, got %v", tt.shouldLog, result)
			}
		})
	}
}

func TestLogger_LogMethods(t *testing.T) {
	tests := []struct {
		name     string
		level    string
		logFunc  func(*Logger)
		expected string
	}{
		{
			name:  "debug message",
			level: LevelDebug,
			logFunc: func(l *Logger) {
				l.Debug("debug message")
			},
			expected: "debug message",
		},
		{
			name:  "info message",
			level: LevelDebug,
			logFunc: func(l *Logger) {
				l.Info("info message")
			},
			expected: "info message",
		},
		{
			name:  "warn message",
			level: LevelDebug,
			logFunc: func(l *Logger) {
				l.Warn("warn message")
			},
			expected: "warn message",
		},
		{
			name:  "error message",
			level: LevelDebug,
			logFunc: func(l *Logger) {
				l.Error("error message")
			},
			expected: "error message",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logger := &Logger{
				level:  tt.level,
				format: "text",
				prefix: "test",
			}

			output := captureLogOutput(t, func() {
				tt.logFunc(logger)
			})

			if !strings.Contains(output, tt.expected) {
				t.Errorf("Expected output to contain %q, got %q", tt.expected, output)
			}
		})
	}
}

func TestLogger_FormattedMethods(t *testing.T) {
	logger := &Logger{
		level:  LevelDebug,
		format: "text",
		prefix: "test",
	}

	tests := []struct {
		name     string
		logFunc  func()
		expected string
	}{
		{
			name: "debugf",
			logFunc: func() {
				logger.Debugf("debug %s %d", "message", 42)
			},
			expected: "debug message 42",
		},
		{
			name: "infof",
			logFunc: func() {
				logger.Infof("info %s %d", "message", 42)
			},
			expected: "info message 42",
		},
		{
			name: "warnf",
			logFunc: func() {
				logger.Warnf("warn %s %d", "message", 42)
			},
			expected: "warn message 42",
		},
		{
			name: "errorf",
			logFunc: func() {
				logger.Errorf("error %s %d", "message", 42)
			},
			expected: "error message 42",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output := captureLogOutput(t, tt.logFunc)

			if !strings.Contains(output, tt.expected) {
				t.Errorf("Expected output to contain %q, got %q", tt.expected, output)
			}
		})
	}
}

func TestLogger_LogFormats(t *testing.T) {
	tests := []struct {
		name           string
		format         string
		expectedFields []string
	}{
		{
			name:           "text format",
			format:         "text",
			expectedFields: []string{"INFO", "test", "test message"},
		},
		{
			name:           "json format",
			format:         "json",
			expectedFields: []string{`"level":"info"`, `"service":"test"`, `"message":"test message"`},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logger := &Logger{
				level:  LevelInfo,
				format: tt.format,
				prefix: "test",
			}

			output := captureLogOutput(t, func() {
				logger.Info("test message")
			})

			for _, field := range tt.expectedFields {
				if !strings.Contains(output, field) {
					t.Errorf("Expected output to contain %q, got %q", field, output)
				}
			}
		})
	}
}

func TestLogger_ErrorWithFields(t *testing.T) {
	logger := &Logger{
		level:  LevelError,
		format: "text",
		prefix: "test",
	}

	fields := map[string]interface{}{
		"user_id": 123,
		"action":  "login",
	}

	output := captureLogOutput(t, func() {
		logger.ErrorWithFields("login failed", fields)
	})

	expectedFields := []string{"login failed", "user_id=123", "action=login"}
	for _, field := range expectedFields {
		if !strings.Contains(output, field) {
			t.Errorf("Expected output to contain %q, got %q", field, output)
		}
	}
}

func TestLogger_LevelFiltering(t *testing.T) {
	logger := &Logger{
		level:  LevelWarn,
		format: "text",
		prefix: "test",
	}

	// Debug and Info should be filtered out
	output := captureLogOutput(t, func() {
		logger.Debug("debug message")
		logger.Info("info message")
		logger.Warn("warn message")
		logger.Error("error message")
	})

	if strings.Contains(output, "debug message") {
		t.Error("Debug message should be filtered out")
	}

	if strings.Contains(output, "info message") {
		t.Error("Info message should be filtered out")
	}

	if !strings.Contains(output, "warn message") {
		t.Error("Warn message should be logged")
	}

	if !strings.Contains(output, "error message") {
		t.Error("Error message should be logged")
	}
}

func TestGlobalFunctions(t *testing.T) {
	// Reset global logger
	globalLogger = &Logger{
		level:  LevelDebug,
		format: "text",
		prefix: "test",
	}

	tests := []struct {
		name     string
		logFunc  func()
		expected string
	}{
		{
			name:     "global Debug",
			logFunc:  func() { Debug("global debug") },
			expected: "global debug",
		},
		{
			name:     "global Debugf",
			logFunc:  func() { Debugf("global %s", "debugf") },
			expected: "global debugf",
		},
		{
			name:     "global Info",
			logFunc:  func() { Info("global info") },
			expected: "global info",
		},
		{
			name:     "global Infof",
			logFunc:  func() { Infof("global %s", "infof") },
			expected: "global infof",
		},
		{
			name:     "global Warn",
			logFunc:  func() { Warn("global warn") },
			expected: "global warn",
		},
		{
			name:     "global Warnf",
			logFunc:  func() { Warnf("global %s", "warnf") },
			expected: "global warnf",
		},
		{
			name:     "global Error",
			logFunc:  func() { Error("global error") },
			expected: "global error",
		},
		{
			name:     "global Errorf",
			logFunc:  func() { Errorf("global %s", "errorf") },
			expected: "global errorf",
		},
		{
			name: "global ErrorWithFields",
			logFunc: func() {
				ErrorWithFields("global error with fields", map[string]interface{}{"key": "value"})
			},
			expected: "global error with fields",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			output := captureLogOutput(t, tt.logFunc)

			if !strings.Contains(output, tt.expected) {
				t.Errorf("Expected output to contain %q, got %q", tt.expected, output)
			}
		})
	}
}
