package handlers

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"chatport-go/internal/client"
)

func TestHealthHandler(t *testing.T) {
	tests := []struct {
		name           string
		clientSetup    func()
		expectedStatus int
		expectedHealth string
	}{
		{
			name: "degraded when client not initialized",
			clientSetup: func() {
				// Ensure client is nil (not initialized)
				client.Client = nil
			},
			expectedStatus: http.StatusServiceUnavailable,
			expectedHealth: "degraded",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Setup
			tt.clientSetup()

			// Create request
			req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
			rr := httptest.NewRecorder()

			// Call handler
			HealthHandler(rr, req)

			// Check status code
			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			// Check content type
			contentType := rr.Header().Get("Content-Type")
			if contentType != "application/json" {
				t.Errorf("Expected Content-Type application/json, got %s", contentType)
			}

			// Parse response
			var response HealthResponse
			err := json.NewDecoder(rr.Body).Decode(&response)
			if err != nil {
				t.Fatalf("Failed to decode response: %v", err)
			}

			// Check health status
			if response.Status != tt.expectedHealth {
				t.Errorf("Expected status %s, got %s", tt.expectedHealth, response.Status)
			}

			// Check required fields
			if response.Version == "" {
				t.Error("Expected version to be present")
			}

			if response.Timestamp.IsZero() {
				t.Error("Expected timestamp to be present")
			}

			// Check timestamp is recent (within last minute)
			if time.Since(response.Timestamp) > time.Minute {
				t.Error("Expected timestamp to be recent")
			}
		})
	}
}

func TestHealthResponse_Structure(t *testing.T) {
	// Test that the health response has the expected structure
	req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
	rr := httptest.NewRecorder()

	HealthHandler(rr, req)

	var response HealthResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Check all required fields are present
	if response.Status == "" {
		t.Error("Status field is missing")
	}

	if response.Version == "" {
		t.Error("Version field is missing")
	}

	if response.Timestamp.IsZero() {
		t.Error("Timestamp field is missing")
	}

	// Check services structure
	// WhatsApp service should be present
	// We can't check the actual values without mocking, but we can check structure
}

func TestHealthHandler_JSONFormat(t *testing.T) {
	req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
	rr := httptest.NewRecorder()

	HealthHandler(rr, req)

	// Verify response is valid JSON
	var result map[string]interface{}
	err := json.NewDecoder(rr.Body).Decode(&result)
	if err != nil {
		t.Fatalf("Response is not valid JSON: %v", err)
	}

	// Check expected top-level keys
	expectedKeys := []string{"status", "timestamp", "version", "services"}
	for _, key := range expectedKeys {
		if _, exists := result[key]; !exists {
			t.Errorf("Expected key %s not found in response", key)
		}
	}
}

func TestHealthHandler_HTTPMethod(t *testing.T) {
	// Test that handler works with GET method
	req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
	rr := httptest.NewRecorder()

	HealthHandler(rr, req)

	if rr.Code == http.StatusMethodNotAllowed {
		t.Error("Handler should accept GET method")
	}
}

func TestHealthHandler_NoBody(t *testing.T) {
	// Test that handler works without request body
	// Set up client to nil to get predictable behavior
	client.Client = nil

	req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
	rr := httptest.NewRecorder()

	HealthHandler(rr, req)

	// Should not fail with 5xx error, but 503 is acceptable for degraded health
	if rr.Code >= 500 && rr.Code != http.StatusServiceUnavailable {
		t.Errorf("Handler should not fail with 5xx error except 503, got status %d", rr.Code)
	}

	// Should return valid JSON
	var response HealthResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Errorf("Response should be valid JSON: %v", err)
	}
}

func TestHealthResponse_JSONMarshaling(t *testing.T) {
	response := HealthResponse{
		Status:    "ok",
		Timestamp: time.Now(),
		Version:   "1.0.0",
		Services: Services{
			WhatsApp: WhatsAppStatus{
				Connected: true,
				JID:       "<EMAIL>",
			},
		},
	}

	// Test marshaling
	data, err := json.Marshal(response)
	if err != nil {
		t.Fatalf("Failed to marshal HealthResponse: %v", err)
	}

	// Test unmarshaling
	var unmarshaled HealthResponse
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal HealthResponse: %v", err)
	}

	// Check values
	if unmarshaled.Status != response.Status {
		t.Errorf("Expected status %s, got %s", response.Status, unmarshaled.Status)
	}

	if unmarshaled.Version != response.Version {
		t.Errorf("Expected version %s, got %s", response.Version, unmarshaled.Version)
	}

	if unmarshaled.Services.WhatsApp.Connected != response.Services.WhatsApp.Connected {
		t.Errorf("Expected WhatsApp connected %v, got %v", response.Services.WhatsApp.Connected, unmarshaled.Services.WhatsApp.Connected)
	}

	if unmarshaled.Services.WhatsApp.JID != response.Services.WhatsApp.JID {
		t.Errorf("Expected WhatsApp JID %s, got %s", response.Services.WhatsApp.JID, unmarshaled.Services.WhatsApp.JID)
	}
}

func TestServices_JSONMarshaling(t *testing.T) {
	services := Services{
		WhatsApp: WhatsAppStatus{
			Connected: true,
			JID:       "<EMAIL>",
		},
	}

	// Test marshaling
	data, err := json.Marshal(services)
	if err != nil {
		t.Fatalf("Failed to marshal Services: %v", err)
	}

	// Test unmarshaling
	var unmarshaled Services
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal Services: %v", err)
	}

	// Check values
	if unmarshaled.WhatsApp.Connected != services.WhatsApp.Connected {
		t.Errorf("Expected WhatsApp connected %v, got %v", services.WhatsApp.Connected, unmarshaled.WhatsApp.Connected)
	}

	if unmarshaled.WhatsApp.JID != services.WhatsApp.JID {
		t.Errorf("Expected WhatsApp JID %s, got %s", services.WhatsApp.JID, unmarshaled.WhatsApp.JID)
	}
}

func TestWhatsAppStatus_JSONMarshaling(t *testing.T) {
	status := WhatsAppStatus{
		Connected: true,
		JID:       "<EMAIL>",
	}

	// Test marshaling
	data, err := json.Marshal(status)
	if err != nil {
		t.Fatalf("Failed to marshal WhatsAppStatus: %v", err)
	}

	// Test unmarshaling
	var unmarshaled WhatsAppStatus
	err = json.Unmarshal(data, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal WhatsAppStatus: %v", err)
	}

	// Check values
	if unmarshaled.Connected != status.Connected {
		t.Errorf("Expected connected %v, got %v", status.Connected, unmarshaled.Connected)
	}

	if unmarshaled.JID != status.JID {
		t.Errorf("Expected JID %s, got %s", status.JID, unmarshaled.JID)
	}
}

func TestHealthHandler_StatusCodes(t *testing.T) {
	tests := []struct {
		name           string
		clientSetup    func()
		expectedStatus int
		expectedHealth string
	}{
		{
			name: "degraded when client nil",
			clientSetup: func() {
				client.Client = nil
			},
			expectedStatus: http.StatusServiceUnavailable,
			expectedHealth: "degraded",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.clientSetup()

			req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
			rr := httptest.NewRecorder()

			HealthHandler(rr, req)

			if rr.Code != tt.expectedStatus {
				t.Errorf("Expected status %d, got %d", tt.expectedStatus, rr.Code)
			}

			var response HealthResponse
			err := json.NewDecoder(rr.Body).Decode(&response)
			if err != nil {
				t.Fatalf("Failed to decode response: %v", err)
			}

			if response.Status != tt.expectedHealth {
				t.Errorf("Expected health status %s, got %s", tt.expectedHealth, response.Status)
			}
		})
	}
}

func TestHealthHandler_VersionInfo(t *testing.T) {
	client.Client = nil // Ensure predictable state

	req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
	rr := httptest.NewRecorder()

	HealthHandler(rr, req)

	var response HealthResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	expectedVersion := "1.0.0"
	if response.Version != expectedVersion {
		t.Errorf("Expected version %s, got %s", expectedVersion, response.Version)
	}
}

func TestHealthHandler_TimestampValidation(t *testing.T) {
	client.Client = nil

	beforeRequest := time.Now()

	req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
	rr := httptest.NewRecorder()

	HealthHandler(rr, req)

	afterRequest := time.Now()

	var response HealthResponse
	err := json.NewDecoder(rr.Body).Decode(&response)
	if err != nil {
		t.Fatalf("Failed to decode response: %v", err)
	}

	// Timestamp should be between before and after request
	if response.Timestamp.Before(beforeRequest) || response.Timestamp.After(afterRequest) {
		t.Errorf("Timestamp %v should be between %v and %v", response.Timestamp, beforeRequest, afterRequest)
	}
}

func TestHealthHandler_ConcurrentRequests(t *testing.T) {
	// Test that health handler can handle concurrent requests
	const numRequests = 10

	client.Client = nil // Ensure predictable state

	results := make([]int, numRequests)
	for i := 0; i < numRequests; i++ {
		go func(index int) {
			req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
			rr := httptest.NewRecorder()

			HealthHandler(rr, req)
			results[index] = rr.Code
		}(i)
	}

	// Wait a bit for requests to complete
	time.Sleep(100 * time.Millisecond)

	// Check that all requests were handled
	for i, code := range results {
		if code == 0 {
			t.Errorf("Request %d was not handled", i)
		}
		// Should be either 200 (ok) or 503 (degraded)
		if code != http.StatusOK && code != http.StatusServiceUnavailable {
			t.Errorf("Request %d returned unexpected status %d", i, code)
		}
	}
}

func TestHealthHandler_ResponseConsistency(t *testing.T) {
	// Test that multiple requests return consistent responses
	client.Client = nil

	var responses []HealthResponse

	for i := 0; i < 3; i++ {
		req := httptest.NewRequest(http.MethodGet, "/api/health", nil)
		rr := httptest.NewRecorder()

		HealthHandler(rr, req)

		var response HealthResponse
		err := json.NewDecoder(rr.Body).Decode(&response)
		if err != nil {
			t.Fatalf("Failed to decode response %d: %v", i, err)
		}

		responses = append(responses, response)
	}

	// Check that status and version are consistent
	for i := 1; i < len(responses); i++ {
		if responses[i].Status != responses[0].Status {
			t.Errorf("Response %d status %s differs from first response status %s", i, responses[i].Status, responses[0].Status)
		}

		if responses[i].Version != responses[0].Version {
			t.Errorf("Response %d version %s differs from first response version %s", i, responses[i].Version, responses[0].Version)
		}

		if responses[i].Services.WhatsApp.Connected != responses[0].Services.WhatsApp.Connected {
			t.Errorf("Response %d WhatsApp connected %v differs from first response %v", i, responses[i].Services.WhatsApp.Connected, responses[0].Services.WhatsApp.Connected)
		}
	}
}
