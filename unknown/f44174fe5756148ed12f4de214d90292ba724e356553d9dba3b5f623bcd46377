# ChatPort API Documentation

This document describes the REST API endpoints provided by the ChatPort WhatsApp service.

## Base URL

```
http://localhost:8081/api
```

## Authentication

Currently, no authentication is required for API endpoints. In production, consider implementing API key authentication or other security measures.

## Content Type

All API endpoints expect and return JSON data unless otherwise specified.

```
Content-Type: application/json
```

## Endpoints

### 1. Send Message

Send a WhatsApp message to a specific number.

**Endpoint:** `POST /api/send`

**Request Body:**

```json
{
  "number": "string", // Required: WhatsApp number (without + prefix)
  "message": "string" // Required: Message text to send
}
```

**Response:**

```json
{
  "success": true,
  "message": "Message sent successfully"
}
```

**Error Response:**

```json
{
  "success": false,
  "error": "Error description"
}
```

**Status Codes:**

- `200 OK`: Message sent successfully
- `400 Bad Request`: Invalid request payload or missing required fields
- `503 Service Unavailable`: WhatsApp client not connected or not initialized

**Example:**

```bash
curl -X POST http://localhost:8081/api/send \
  -H "Content-Type: application/json" \
  -d '{
    "number": "**********",
    "message": "Hello from ChatPort!"
  }'
```

### 2. Health Check

Get the health status of the service and its dependencies.

**Endpoint:** `GET /api/health`

**Response:**

```json
{
  "status": "ok", // "ok" or "degraded"
  "timestamp": "2024-01-01T12:00:00Z",
  "version": "1.0.0",
  "services": {
    "whatsapp": {
      "connected": true,
      "jid": "<EMAIL>"
    }
  }
}
```

**Status Codes:**

- `200 OK`: Service is healthy
- `503 Service Unavailable`: Service is degraded (WhatsApp not connected)

**Example:**

```bash
curl http://localhost:8081/api/health
```

### 3. Service Status

Get detailed status information about the service.

**Endpoint:** `GET /api/status`

**Response:**

```json
{
  "application": {
    "name": "ChatPort WhatsApp Service",
    "version": "1.0.0",
    "uptime": "2h30m15s"
  },
  "system": {
    "go_version": "go1.24.2",
    "num_goroutine": 12,
    "num_cpu": 8
  },
  "whatsapp": {
    "connected": true,
    "jid": "<EMAIL>",
    "status": "connected"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**

- `200 OK`: Always returns 200 with current status

**Example:**

```bash
curl http://localhost:8081/api/status
```

### 4. Metrics

Get operational metrics and statistics.

**Endpoint:** `GET /api/metrics`

**Response:**

```json
{
  "metrics": {
    "messages_received": 150,
    "messages_sent": 145,
    "messages_failed": 5,
    "ai_service_calls": 150,
    "ai_service_failures": 2,
    "http_requests": 300,
    "http_errors": 8,
    "whatsapp_reconnects": 1,
    "last_message_time": "2024-01-01T11:58:30Z",
    "last_ai_service_call": "2024-01-01T11:58:32Z",
    "start_time": "2024-01-01T09:30:00Z"
  },
  "uptime": "2h30m15s",
  "rates": {
    "message_success_rate": 96.67,
    "ai_service_success_rate": 98.67,
    "http_success_rate": 97.33
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

**Status Codes:**

- `200 OK`: Always returns 200 with current metrics

**Example:**

```bash
curl http://localhost:8081/api/metrics
```

## Error Handling

All endpoints follow a consistent error response format:

```json
{
  "success": false,
  "error": "Human-readable error message"
}
```

### Common Error Codes

- `400 Bad Request`: Invalid request format or missing required fields
- `500 Internal Server Error`: Unexpected server error
- `503 Service Unavailable`: Service dependencies not available

## Rate Limiting

Currently, no rate limiting is implemented. Consider implementing rate limiting in production environments.

## CORS

CORS is enabled for all origins (`*`). In production, configure CORS to allow only specific origins.

## Request/Response Examples

### Successful Message Send

**Request:**

```bash
curl -X POST http://localhost:8081/api/send \
  -H "Content-Type: application/json" \
  -d '{
    "number": "**********",
    "message": "Your prescription is ready for pickup!"
  }'
```

**Response:**

```json
{
  "success": true,
  "message": "Message sent successfully"
}
```

### Error: Missing Required Field

**Request:**

```bash
curl -X POST http://localhost:8081/api/send \
  -H "Content-Type: application/json" \
  -d '{
    "number": "**********"
  }'
```

**Response:**

```json
{
  "success": false,
  "error": "Number and message are required"
}
```

### Error: Service Unavailable

**Request:**

```bash
curl -X POST http://localhost:8081/api/send \
  -H "Content-Type: application/json" \
  -d '{
    "number": "**********",
    "message": "Hello!"
  }'
```

**Response (when WhatsApp not connected):**

```json
{
  "success": false,
  "error": "WhatsApp client not connected"
}
```

## Integration Notes

### Message Flow

1. Client sends message via `/api/send`
2. ChatPort validates request
3. ChatPort checks WhatsApp connection
4. Message is sent via WhatsApp
5. Response is returned to client

### Incoming Messages

Incoming WhatsApp messages are automatically:

1. Received by ChatPort
2. Broadcasted to WebSocket clients for processing
3. No automatic responses are generated by ChatPort

### Monitoring Integration

Use the `/api/health` and `/api/metrics` endpoints for:

- Health checks in load balancers
- Monitoring system integration
- Alerting on service degradation
- Performance tracking

## Security Considerations

1. **Network Security**: Deploy behind a firewall or VPN
2. **API Authentication**: Implement API keys or OAuth in production
3. **Input Validation**: All inputs are validated server-side
4. **Rate Limiting**: Consider implementing rate limiting
5. **HTTPS**: Use HTTPS in production environments
6. **Logging**: Sensitive data is not logged

## Changelog

### Version 1.0.0

- Initial API implementation
- Basic message sending functionality
- Health and status endpoints
- Metrics collection
