#!/usr/bin/env python3
"""
Asynchronous Server Gateway Interface for Wellbot AI Service
"""

import sys
from pathlib import Path

from main import app

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))


def main():
    """Main entry point"""
    print("🚀 Starting Wellbot AI Service...")

    # Check if .env file exists
    env_file = current_dir / ".env"
    if not env_file.exists():
        print("⚠️  Warning: .env file not found!")
        print("   Please copy .env.example to .env and configure your settings.")
        print("   Continuing with default configuration...")
        print()

    # Import and run the FastAPI app
    try:
        import uvicorn

        # Get configuration
        from config.settings import get_settings

        settings = get_settings()

        print(f"📡 Service: {settings.app_name}")
        print(f"🔗 URL: http://{settings.host}:{settings.port}")
        print(f"📚 Docs: http://{settings.host}:{settings.port}/docs")
        print(f"🔧 Debug: {settings.debug}")
        print()

        # Run the server
        uvicorn.run(
            app,
            host=settings.host,
            port=settings.port,
            reload=settings.debug,
            log_level="info" if not settings.debug else "debug",
        )

    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure all dependencies are installed:")
        print("   uv sync  # or pip install -e .")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Failed to start service: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
