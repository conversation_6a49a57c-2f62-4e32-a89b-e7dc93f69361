package websocket

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/gorilla/websocket"
)

// TestWebSocketIntegration tests the complete WebSocket flow
func TestWebSocketIntegration(t *testing.T) {
	// Create hub and handler
	hub := NewHub(DefaultHubConfig())
	go hub.Run()
	defer hub.Stop()

	handler := NewHandler(hub, nil)

	// Create test server
	server := httptest.NewServer(handler)
	defer server.Close()

	// Connect via WebSocket
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http")
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		t.Fatalf("Failed to connect to WebSocket: %v", err)
	}
	defer conn.Close()

	// Should receive welcome message
	_, message, err := conn.ReadMessage()
	if err != nil {
		t.Fatalf("Failed to read welcome message: %v", err)
	}

	// Parse welcome message
	var welcomeMsg Message
	err = json.Unmarshal(message, &welcomeMsg)
	if err != nil {
		t.Fatalf("Failed to parse welcome message: %v", err)
	}

	if welcomeMsg.Type != "welcome" {
		t.Errorf("Expected welcome message, got %s", welcomeMsg.Type)
	}

	// Verify client was registered
	time.Sleep(10 * time.Millisecond)
	if hub.GetClientCount() != 1 {
		t.Errorf("Expected 1 client to be registered, got %d", hub.GetClientCount())
	}

	// Test subscription
	subscribeMsg := Message{
		Type:      MessageTypeSubscribe,
		Timestamp: time.Now(),
		Data:      json.RawMessage(`{"action":"subscribe","subscription":"whatsapp"}`),
	}

	subscribeData, err := subscribeMsg.ToJSON()
	if err != nil {
		t.Fatalf("Failed to marshal subscribe message: %v", err)
	}

	err = conn.WriteMessage(websocket.TextMessage, subscribeData)
	if err != nil {
		t.Fatalf("Failed to send subscribe message: %v", err)
	}

	// Test ping-pong
	pingMsg := Message{
		Type:      MessageTypePing,
		Timestamp: time.Now(),
	}

	pingData, err := pingMsg.ToJSON()
	if err != nil {
		t.Fatalf("Failed to marshal ping message: %v", err)
	}

	err = conn.WriteMessage(websocket.TextMessage, pingData)
	if err != nil {
		t.Fatalf("Failed to send ping message: %v", err)
	}

	// Should receive pong response
	_, pongMessage, err := conn.ReadMessage()
	if err != nil {
		t.Fatalf("Failed to read pong message: %v", err)
	}

	var pongMsg Message
	err = json.Unmarshal(pongMessage, &pongMsg)
	if err != nil {
		t.Fatalf("Failed to parse pong message: %v", err)
	}

	if pongMsg.Type != MessageTypePong {
		t.Errorf("Expected pong message, got %s", pongMsg.Type)
	}
}

// TestWebSocketStatsEndpoint tests the stats endpoint with real WebSocket connections
func TestWebSocketStatsEndpoint(t *testing.T) {
	// Create hub and handler
	hub := NewHub(DefaultHubConfig())
	go hub.Run()
	defer hub.Stop()

	handler := NewHandler(hub, nil)

	// Create test server with both WebSocket and HTTP endpoints
	mux := http.NewServeMux()
	mux.HandleFunc("/ws", handler.ServeHTTP)
	mux.HandleFunc("/api/ws/stats", handler.WebSocketStatsHandler)

	server := httptest.NewServer(mux)
	defer server.Close()

	// Connect a WebSocket client
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http") + "/ws"
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		t.Fatalf("Failed to connect to WebSocket: %v", err)
	}
	defer conn.Close()

	// Read welcome message to ensure connection is established
	_, _, err = conn.ReadMessage()
	if err != nil {
		t.Fatalf("Failed to read welcome message: %v", err)
	}

	// Wait for client registration
	time.Sleep(20 * time.Millisecond)

	// Test stats endpoint
	resp, err := http.Get(server.URL + "/api/ws/stats")
	if err != nil {
		t.Fatalf("Failed to get stats: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		t.Errorf("Expected status 200, got %d", resp.StatusCode)
	}

	// Parse stats response
	var stats map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&stats)
	if err != nil {
		t.Fatalf("Failed to decode stats response: %v", err)
	}

	// Verify stats show the connected client
	if hubStats, ok := stats["hub_stats"].(map[string]interface{}); ok {
		if currentConnections, ok := hubStats["current_connections"].(float64); ok {
			if int(currentConnections) != 1 {
				t.Errorf("Expected 1 current connection, got %v", currentConnections)
			}
		} else {
			t.Error("Expected current_connections to be a number")
		}
	} else {
		t.Error("Expected hub_stats in response")
	}

	// Verify clients array
	if clients, ok := stats["clients"].([]interface{}); ok {
		if len(clients) != 1 {
			t.Errorf("Expected 1 client in stats, got %d", len(clients))
		}
	} else {
		t.Error("Expected clients array in response")
	}
}

// TestWebSocketBroadcasting tests message broadcasting functionality
func TestWebSocketBroadcasting(t *testing.T) {
	// Create hub and handler
	hub := NewHub(DefaultHubConfig())
	go hub.Run()
	defer hub.Stop()

	handler := NewHandler(hub, nil)

	// Create test server
	server := httptest.NewServer(handler)
	defer server.Close()

	// Connect multiple WebSocket clients
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http")

	conn1, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		t.Fatalf("Failed to connect client 1: %v", err)
	}
	defer conn1.Close()

	conn2, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	if err != nil {
		t.Fatalf("Failed to connect client 2: %v", err)
	}
	defer conn2.Close()

	// Read welcome messages
	_, _, err = conn1.ReadMessage()
	if err != nil {
		t.Fatalf("Failed to read welcome message from client 1: %v", err)
	}

	_, _, err = conn2.ReadMessage()
	if err != nil {
		t.Fatalf("Failed to read welcome message from client 2: %v", err)
	}

	// Wait for clients to be registered
	time.Sleep(20 * time.Millisecond)

	// Subscribe both clients to WhatsApp messages
	subscribeMsg := Message{
		Type:      MessageTypeSubscribe,
		Timestamp: time.Now(),
		Data:      json.RawMessage(`{"action":"subscribe","subscription":"whatsapp"}`),
	}

	subscribeData, err := subscribeMsg.ToJSON()
	if err != nil {
		t.Fatalf("Failed to marshal subscribe message: %v", err)
	}

	err = conn1.WriteMessage(websocket.TextMessage, subscribeData)
	if err != nil {
		t.Fatalf("Failed to send subscribe message to client 1: %v", err)
	}

	err = conn2.WriteMessage(websocket.TextMessage, subscribeData)
	if err != nil {
		t.Fatalf("Failed to send subscribe message to client 2: %v", err)
	}

	// Wait for subscriptions to be processed
	time.Sleep(10 * time.Millisecond)

	// Broadcast a WhatsApp message
	whatsappMsg, err := NewIncomingWhatsAppMessage("1234567890", "Test broadcast message", "msg123")
	if err != nil {
		t.Fatalf("Failed to create WhatsApp message: %v", err)
	}

	hub.BroadcastToSubscription(whatsappMsg, SubscriptionWhatsApp)

	// Both clients should receive the message
	// Check client 1 receives the message
	conn1.SetReadDeadline(time.Now().Add(500 * time.Millisecond))
	_, message1, err := conn1.ReadMessage()
	if err != nil {
		t.Fatalf("Client 1 failed to receive broadcast message: %v", err)
	}

	var receivedMsg1 Message
	err = json.Unmarshal(message1, &receivedMsg1)
	if err != nil {
		t.Fatalf("Failed to parse message from client 1: %v", err)
	}

	if receivedMsg1.Type != MessageTypeIncomingWhatsApp {
		t.Errorf("Expected WhatsApp message type, got %s", receivedMsg1.Type)
	}

	// Check client 2 receives the message
	conn2.SetReadDeadline(time.Now().Add(500 * time.Millisecond))
	_, message2, err := conn2.ReadMessage()
	if err != nil {
		t.Fatalf("Client 2 failed to receive broadcast message: %v", err)
	}

	var receivedMsg2 Message
	err = json.Unmarshal(message2, &receivedMsg2)
	if err != nil {
		t.Fatalf("Failed to parse message from client 2: %v", err)
	}

	if receivedMsg2.Type != MessageTypeIncomingWhatsApp {
		t.Errorf("Expected WhatsApp message type, got %s", receivedMsg2.Type)
	}

	// Verify both clients are still connected
	if hub.GetClientCount() != 2 {
		t.Errorf("Expected 2 clients still connected, got %d", hub.GetClientCount())
	}
}
