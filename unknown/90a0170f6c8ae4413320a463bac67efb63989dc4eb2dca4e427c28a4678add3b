package websocket

import (
	"context"
	"testing"
	"time"
)

func TestNewBroadcaster(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	config := &BroadcasterConfig{
		MetricsInterval:        5 * time.Second,
		BufferSize:             50,
		EnableMetricsBroadcast: true,
		EnableHealthBroadcast:  true,
	}

	broadcaster := NewBroadcaster(hub, config)

	if broadcaster == nil {
		t.Fatal("Expected broadcaster to be created")
	}

	if broadcaster.hub != hub {
		t.<PERSON>r("Expected broadcaster to reference the provided hub")
	}

	if broadcaster.config != config {
		t.Error("Expected broadcaster to reference the provided config")
	}

	if cap(broadcaster.whatsappChan) != config.BufferSize {
		t.<PERSON>("Expected WhatsApp channel buffer size %d, got %d", config.BufferSize, cap(broadcaster.whatsappChan))
	}
}

func TestNewBroadcaster_DefaultConfig(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	broadcaster := NewBroadcaster(hub, nil)

	if broadcaster == nil {
		t.Fatal("Expected broadcaster to be created with default config")
	}

	if broadcaster.config == nil {
		t.Error("Expected broadcaster to have default config")
	}

	defaultConfig := DefaultBroadcasterConfig()
	if broadcaster.config.MetricsInterval != defaultConfig.MetricsInterval {
		t.Errorf("Expected default metrics interval %v, got %v", defaultConfig.MetricsInterval, broadcaster.config.MetricsInterval)
	}
}

func TestDefaultBroadcasterConfig(t *testing.T) {
	config := DefaultBroadcasterConfig()

	if config.MetricsInterval <= 0 {
		t.Error("Expected positive MetricsInterval")
	}

	if config.BufferSize <= 0 {
		t.Error("Expected positive BufferSize")
	}

	if !config.EnableMetricsBroadcast {
		t.Error("Expected EnableMetricsBroadcast to be true by default")
	}

	if !config.EnableHealthBroadcast {
		t.Error("Expected EnableHealthBroadcast to be true by default")
	}
}

func TestBroadcaster_BroadcastWhatsAppMessage(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	go hub.Run()
	defer hub.Stop()

	broadcaster := NewBroadcaster(hub, DefaultBroadcasterConfig())
	go broadcaster.Run()
	defer broadcaster.Stop()

	// Create a client subscribed to WhatsApp messages
	client := &Client{
		id:            "test-client",
		send:          make(chan []byte, 10),
		ctx:           context.Background(),
		connectedAt:   time.Now(),
		lastActivity:  time.Now(),
		subscriptions: map[SubscriptionType]bool{SubscriptionWhatsApp: true},
	}

	hub.RegisterClient(client)
	time.Sleep(10 * time.Millisecond)

	// Broadcast WhatsApp message
	broadcaster.BroadcastWhatsAppMessage("**********", "Hello, World!", "msg123")

	// Wait for message to be processed
	time.Sleep(50 * time.Millisecond)

	// Client should receive the message
	select {
	case data := <-client.send:
		msg, err := ParseMessage(data)
		if err != nil {
			t.Fatalf("Failed to parse received message: %v", err)
		}

		if msg.Type != MessageTypeIncomingWhatsApp {
			t.Errorf("Expected message type %s, got %s", MessageTypeIncomingWhatsApp, msg.Type)
		}

		var whatsappMsg IncomingWhatsAppMessage
		err = msg.GetData(&whatsappMsg)
		if err != nil {
			t.Fatalf("Failed to get WhatsApp message data: %v", err)
		}

		if whatsappMsg.From != "**********" {
			t.Errorf("Expected from '**********', got %s", whatsappMsg.From)
		}

		if whatsappMsg.Message != "Hello, World!" {
			t.Errorf("Expected message 'Hello, World!', got %s", whatsappMsg.Message)
		}

		if whatsappMsg.MessageID != "msg123" {
			t.Errorf("Expected message ID 'msg123', got %s", whatsappMsg.MessageID)
		}

	case <-time.After(100 * time.Millisecond):
		t.Error("Expected to receive WhatsApp message")
	}
}

func TestBroadcaster_BroadcastStatusUpdate(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	go hub.Run()
	defer hub.Stop()

	broadcaster := NewBroadcaster(hub, DefaultBroadcasterConfig())
	go broadcaster.Run()
	defer broadcaster.Stop()

	// Create a client subscribed to status updates
	client := &Client{
		id:            "test-client",
		send:          make(chan []byte, 10),
		ctx:           context.Background(),
		connectedAt:   time.Now(),
		lastActivity:  time.Now(),
		subscriptions: map[SubscriptionType]bool{SubscriptionStatus: true},
	}

	hub.RegisterClient(client)
	time.Sleep(10 * time.Millisecond)

	// Broadcast status update
	broadcaster.BroadcastStatusUpdate("whatsapp", "connected", "Connection established")

	// Wait for message to be processed
	time.Sleep(50 * time.Millisecond)

	// Client should receive the message
	select {
	case data := <-client.send:
		msg, err := ParseMessage(data)
		if err != nil {
			t.Fatalf("Failed to parse received message: %v", err)
		}

		if msg.Type != MessageTypeStatusUpdate {
			t.Errorf("Expected message type %s, got %s", MessageTypeStatusUpdate, msg.Type)
		}

		var statusMsg StatusUpdate
		err = msg.GetData(&statusMsg)
		if err != nil {
			t.Fatalf("Failed to get status message data: %v", err)
		}

		if statusMsg.Service != "whatsapp" {
			t.Errorf("Expected service 'whatsapp', got %s", statusMsg.Service)
		}

		if statusMsg.Status != "connected" {
			t.Errorf("Expected status 'connected', got %s", statusMsg.Status)
		}

		if statusMsg.Details != "Connection established" {
			t.Errorf("Expected details 'Connection established', got %s", statusMsg.Details)
		}

	case <-time.After(100 * time.Millisecond):
		t.Error("Expected to receive status update message")
	}
}

func TestBroadcaster_BroadcastHealthUpdate(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	go hub.Run()
	defer hub.Stop()

	broadcaster := NewBroadcaster(hub, DefaultBroadcasterConfig())
	go broadcaster.Run()
	defer broadcaster.Stop()

	// Create a client subscribed to health updates
	client := &Client{
		id:            "test-client",
		send:          make(chan []byte, 10),
		ctx:           context.Background(),
		connectedAt:   time.Now(),
		lastActivity:  time.Now(),
		subscriptions: map[SubscriptionType]bool{SubscriptionHealth: true},
	}

	hub.RegisterClient(client)
	time.Sleep(10 * time.Millisecond)

	// Broadcast health update
	services := map[string]interface{}{
		"whatsapp": map[string]interface{}{
			"connected": true,
			"jid":       "<EMAIL>",
		},
	}
	broadcaster.BroadcastHealthUpdate("ok", services)

	// Wait for message to be processed
	time.Sleep(50 * time.Millisecond)

	// Client should receive the message
	select {
	case data := <-client.send:
		msg, err := ParseMessage(data)
		if err != nil {
			t.Fatalf("Failed to parse received message: %v", err)
		}

		if msg.Type != MessageTypeHealthUpdate {
			t.Errorf("Expected message type %s, got %s", MessageTypeHealthUpdate, msg.Type)
		}

		var healthMsg HealthUpdate
		err = msg.GetData(&healthMsg)
		if err != nil {
			t.Fatalf("Failed to get health message data: %v", err)
		}

		if healthMsg.Status != "ok" {
			t.Errorf("Expected status 'ok', got %s", healthMsg.Status)
		}

		if healthMsg.Services == nil {
			t.Error("Expected services to be present")
		}

	case <-time.After(100 * time.Millisecond):
		t.Error("Expected to receive health update message")
	}

	// Broadcast same status again - should not send duplicate
	broadcaster.BroadcastHealthUpdate("ok", services)
	time.Sleep(50 * time.Millisecond)

	// Should not receive another message
	select {
	case <-client.send:
		t.Error("Should not receive duplicate health update")
	case <-time.After(50 * time.Millisecond):
		// Expected - no duplicate message
	}

	// Broadcast different status - should send
	broadcaster.BroadcastHealthUpdate("degraded", services)
	time.Sleep(50 * time.Millisecond)

	// Should receive the new status
	select {
	case data := <-client.send:
		msg, err := ParseMessage(data)
		if err != nil {
			t.Fatalf("Failed to parse received message: %v", err)
		}

		var healthMsg HealthUpdate
		err = msg.GetData(&healthMsg)
		if err != nil {
			t.Fatalf("Failed to get health message data: %v", err)
		}

		if healthMsg.Status != "degraded" {
			t.Errorf("Expected status 'degraded', got %s", healthMsg.Status)
		}

		if healthMsg.PreviousStatus != "ok" {
			t.Errorf("Expected previous status 'ok', got %s", healthMsg.PreviousStatus)
		}

	case <-time.After(100 * time.Millisecond):
		t.Error("Expected to receive health status change")
	}
}

func TestBroadcaster_Stop(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	broadcaster := NewBroadcaster(hub, DefaultBroadcasterConfig())

	// Start broadcaster
	go broadcaster.Run()

	// Stop broadcaster
	broadcaster.Stop()

	// Context should be cancelled
	select {
	case <-broadcaster.ctx.Done():
		// Expected
	case <-time.After(100 * time.Millisecond):
		t.Error("Expected broadcaster context to be cancelled")
	}
}

func TestBroadcaster_GetStats(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	broadcaster := NewBroadcaster(hub, DefaultBroadcasterConfig())

	stats := broadcaster.GetStats()

	if stats["whatsapp_queue_size"] == nil {
		t.Error("Expected whatsapp_queue_size in stats")
	}

	if stats["status_queue_size"] == nil {
		t.Error("Expected status_queue_size in stats")
	}

	if stats["metrics_queue_size"] == nil {
		t.Error("Expected metrics_queue_size in stats")
	}

	if stats["health_queue_size"] == nil {
		t.Error("Expected health_queue_size in stats")
	}

	if stats["config"] == nil {
		t.Error("Expected config in stats")
	}
}

func TestBroadcaster_ChannelFull(t *testing.T) {
	hub := NewHub(DefaultHubConfig())

	// Create broadcaster with very small buffer
	config := &BroadcasterConfig{
		MetricsInterval:        10 * time.Second,
		BufferSize:             1, // Very small buffer
		EnableMetricsBroadcast: true,
		EnableHealthBroadcast:  true,
	}

	broadcaster := NewBroadcaster(hub, config)

	// Fill the WhatsApp channel
	broadcaster.BroadcastWhatsAppMessage("test1", "msg1", "id1")

	// This should not block (should drop the message)
	done := make(chan bool, 1)
	go func() {
		broadcaster.BroadcastWhatsAppMessage("test2", "msg2", "id2")
		done <- true
	}()

	select {
	case <-done:
		// Expected - should not block
	case <-time.After(100 * time.Millisecond):
		t.Error("BroadcastWhatsAppMessage should not block when channel is full")
	}
}

func TestGlobalBroadcaster(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	config := DefaultBroadcasterConfig()

	// Initialize global broadcaster
	InitGlobalBroadcaster(hub, config)

	// Get global broadcaster
	globalBroadcaster := GetGlobalBroadcaster()
	if globalBroadcaster == nil {
		t.Error("Expected global broadcaster to be initialized")
	}

	// Test convenience functions
	BroadcastWhatsApp("test", "message", "id")
	BroadcastStatus("service", "status", "details")
	BroadcastHealth("ok", map[string]interface{}{})

	// These should not panic and should work with the global broadcaster
}

func TestBroadcaster_MetricsChanged(t *testing.T) {
	hub := NewHub(DefaultHubConfig())
	broadcaster := NewBroadcaster(hub, DefaultBroadcasterConfig())

	old := &MetricsUpdate{
		MessagesReceived: 10,
		MessagesSent:     8,
		HTTPRequests:     20,
	}

	// Same metrics - should not have changed
	same := &MetricsUpdate{
		MessagesReceived: 10,
		MessagesSent:     8,
		HTTPRequests:     20,
	}

	if broadcaster.metricsChanged(old, same) {
		t.Error("Expected metrics not to have changed")
	}

	// Different metrics - should have changed
	different := &MetricsUpdate{
		MessagesReceived: 11, // Changed
		MessagesSent:     8,
		HTTPRequests:     20,
	}

	if !broadcaster.metricsChanged(old, different) {
		t.Error("Expected metrics to have changed")
	}
}
