package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gorilla/websocket"
)

func main() {
	// Test WebSocket stats endpoint first
	fmt.Println("Testing WebSocket stats endpoint...")
	resp, err := http.Get("http://localhost:8081/api/ws/stats")
	if err != nil {
		log.Fatalf("Failed to get WebSocket stats: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Fatalf("WebSocket stats endpoint returned status %d", resp.StatusCode)
	}

	var stats map[string]any
	err = json.NewDecoder(resp.Body).Decode(&stats)
	if err != nil {
		log.Fatalf("Failed to decode stats response: %v", err)
	}

	fmt.Printf("✅ WebSocket stats endpoint working. Current connections: %v\n",
		stats["hub_stats"].(map[string]interface{})["current_connections"])

	// Test WebSocket connection
	fmt.Println("\nTesting WebSocket connection...")

	dialer := websocket.Dialer{
		HandshakeTimeout: 10 * time.Second,
	}

	conn, resp, err := dialer.Dial("ws://localhost:8081/ws", nil)
	if err != nil {
		log.Printf("Failed to connect to WebSocket: %v", err)
		if resp != nil {
			log.Printf("Response status: %s", resp.Status)
			log.Printf("Response headers: %v", resp.Header)
			body := make([]byte, 1024)
			n, _ := resp.Body.Read(body)
			if n > 0 {
				log.Printf("Response body: %s", string(body[:n]))
			}
		}
		log.Fatalf("WebSocket connection failed")
	}
	defer conn.Close()

	fmt.Println("✅ WebSocket connection established successfully!")

	// Read welcome message
	_, message, err := conn.ReadMessage()
	if err != nil {
		log.Fatalf("Failed to read welcome message: %v", err)
	}

	var welcomeMsg map[string]interface{}
	err = json.Unmarshal(message, &welcomeMsg)
	if err != nil {
		log.Fatalf("Failed to parse welcome message: %v", err)
	}

	fmt.Printf("✅ Received welcome message: %s\n", welcomeMsg["type"])

	// Test subscription
	fmt.Println("\nTesting subscription...")
	subscribeMsg := map[string]interface{}{
		"type":      "subscribe",
		"timestamp": time.Now().Format(time.RFC3339),
		"data": map[string]interface{}{
			"action":       "subscribe",
			"subscription": "whatsapp",
		},
	}

	subscribeData, err := json.Marshal(subscribeMsg)
	if err != nil {
		log.Fatalf("Failed to marshal subscribe message: %v", err)
	}

	err = conn.WriteMessage(websocket.TextMessage, subscribeData)
	if err != nil {
		log.Fatalf("Failed to send subscribe message: %v", err)
	}

	fmt.Println("✅ Subscription message sent successfully!")

	// Test ping-pong
	fmt.Println("\nTesting ping-pong...")
	pingMsg := map[string]interface{}{
		"type":      "ping",
		"timestamp": time.Now().Format(time.RFC3339),
	}

	pingData, err := json.Marshal(pingMsg)
	if err != nil {
		log.Fatalf("Failed to marshal ping message: %v", err)
	}

	err = conn.WriteMessage(websocket.TextMessage, pingData)
	if err != nil {
		log.Fatalf("Failed to send ping message: %v", err)
	}

	// Set read deadline for pong response
	conn.SetReadDeadline(time.Now().Add(5 * time.Second))
	_, pongMessage, err := conn.ReadMessage()
	if err != nil {
		log.Fatalf("Failed to read pong message: %v", err)
	}

	var pongMsg map[string]interface{}
	err = json.Unmarshal(pongMessage, &pongMsg)
	if err != nil {
		log.Fatalf("Failed to parse pong message: %v", err)
	}

	if pongMsg["type"] == "pong" {
		fmt.Println("✅ Ping-pong test successful!")
	} else {
		fmt.Printf("⚠️  Expected pong, got: %s\n", pongMsg["type"])
	}

	// Test stats endpoint again to see the connection
	fmt.Println("\nChecking updated stats...")
	resp2, err := http.Get("http://localhost:8081/api/ws/stats")
	if err != nil {
		log.Fatalf("Failed to get updated WebSocket stats: %v", err)
	}
	defer resp2.Body.Close()

	var stats2 map[string]interface{}
	err = json.NewDecoder(resp2.Body).Decode(&stats2)
	if err != nil {
		log.Fatalf("Failed to decode updated stats response: %v", err)
	}

	currentConnections := stats2["hub_stats"].(map[string]interface{})["current_connections"]
	fmt.Printf("✅ Current connections in stats: %v\n", currentConnections)

	if clients, ok := stats2["clients"].([]interface{}); ok {
		fmt.Printf("✅ Number of clients in stats: %d\n", len(clients))
		if len(clients) > 0 {
			if client, ok := clients[0].(map[string]interface{}); ok {
				fmt.Printf("   Client ID: %v\n", client["id"])
				fmt.Printf("   Remote Address: %v\n", client["remote_addr"])
				fmt.Printf("   Subscriptions: %v\n", client["subscriptions"])
			}
		}
	}

	fmt.Println("\n🎉 All WebSocket tests passed successfully!")
	fmt.Println("The WebSocket connection issue has been resolved.")
}
