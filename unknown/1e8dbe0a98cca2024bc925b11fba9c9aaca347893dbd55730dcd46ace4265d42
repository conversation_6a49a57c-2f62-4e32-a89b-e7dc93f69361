use std::sync::Arc;

use db_migration::run_migrations;
use derive_getters::Getters;
use sea_orm::{Database, DatabaseConnection};
use serde::{Deserialize, Serialize};
use typed_builder::TypedBuilder;

mod error;
pub use error::ServiceError;

/// Service manager containing all application services
#[derive(Getters, TypedBuilder)]
pub struct ServiceManager {
    #[builder(setter(into))]
    db: Arc<DatabaseConnection>,
}

impl ServiceManager {
    /// Creates a new instance of ServiceManager with all required services.
    ///
    /// This method initializes all service implementations with a shared database connection.
    /// The connection is wrapped in an Arc to allow safe sharing across services.
    ///
    /// # Arguments
    /// * `db` - Thread-safe reference to the database connection
    ///
    /// # Returns
    /// * `Result<Self, ServiceError>` - New ServiceManager instance or error if initialization fails
    ///
    /// # Errors
    /// Returns `ServiceError` if any service initialization fails
    async fn try_new(db: Arc<DatabaseConnection>) -> Result<Self, ServiceError> {
        Ok(Self::builder().db(db.clone()).build())
    }

    /// Creates a new instance of ServiceManager with all required services and wraps it in an Arc.
    ///
    /// # Arguments
    /// * `db` - Database connection
    ///
    /// # Returns
    /// * `Result<Arc<Self>, ServiceError>` - New ServiceManager instance wrapped in Arc or error
    pub async fn new(db: DatabaseConnection) -> Result<Arc<Self>, ServiceError> {
        let db = Arc::new(db);
        let service_manager = Self::try_new(db).await?;
        Ok(Arc::new(service_manager))
    }
}

/// Sets up all services for the application
pub async fn setup_services(url: &str) -> Result<ServiceManager, ServiceError> {
    let db = Database::connect(url).await?;

    // Run migrations with error handling
    match run_migrations(&db).await {
        Ok(_) => {
            tracing::info!("Migrations completed successfully");
        }
        Err(e) => {
            tracing::warn!(
                "Migration error (this might be expected if table already exists): {:?}",
                e
            );
            // Continue anyway - the table might already exist with the correct schema
            // or we might be able to work with the existing schema
        }
    }

    ServiceManager::try_new(Arc::new(db)).await
}

/// Pagination parameters for API requests
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationParams {
    pub page: u64,
    pub page_size: u64,
}

/// Pagination parameters for API requests
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationParamsDto {
    pub page: u64,
    pub page_size: u64,
}

impl From<PaginationParamsDto> for PaginationParams {
    fn from(dto: PaginationParamsDto) -> Self {
        Self {
            page: dto.page,
            page_size: dto.page_size,
        }
    }
}

/// Pagination result for API responses
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PaginationResult<T> {
    pub data: T,
    pub total: u64,
    pub page: u64,
    pub page_size: u64,
}

impl Default for PaginationParams {
    fn default() -> Self {
        Self {
            page: 1,
            page_size: 10,
        }
    }
}
